@extends('layouts.app')

@section('content')
<div class="container mx-auto max-w-[1280px] p-6">
    <h1 class="text-2xl font-bold mb-6 text-light-text dark:text-dark-text">Dashboard Admin</h1>

    <!-- Statistik Utama -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Card <PERSON> -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Total Customer</h2>
            </div>
            <div class="p-6 flex items-center">
                <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-4">
                    <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-3xl font-bold text-gray-800 dark:text-gray-100">{{ $customerCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Registered customers</p>
                </div>
            </div>
        </div>

        <!-- Card Jumlah Proyek -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-light-accent dark:bg-dark-accent p-4">
                <h2 class="text-white text-lg font-semibold">Total Proyek</h2>
            </div>
            <div class="p-6 flex items-center">
                <div class="p-3 bg-indigo-100 dark:bg-indigo-900/30 rounded-full mr-4">
                    <svg class="w-8 h-8 text-indigo-600 dark:text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-3xl font-bold text-gray-800 dark:text-gray-100">{{ $projectCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Active projects</p>
                </div>
            </div>
        </div>

        <!-- Card Jumlah AHS -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-green-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Total AHS</h2>
            </div>
            <div class="p-6 flex items-center">
                <div class="p-3 bg-green-100 dark:bg-blue-900/30 rounded-full mr-4">
                    <svg class="w-8 h-8 text-green-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.96 2.96 0 0 0 .13 5H5Z"/>
                        <path d="M6.737 11.061a2.961 2.961 0 0 1 .81-1.515l6.117-6.116A4.839 4.839 0 0 1 16 2.141V2a1.97 1.97 0 0 0-1.933-2H7v5a2 2 0 0 1-2 2H0v11a1.969 1.969 0 0 0 1.933 2h12.134A1.97 1.97 0 0 0 16 18v-3.093l-1.546 1.546c-.413.413-.94.695-1.513.81l-3.4.679a2.947 2.947 0 0 1-1.85-.227 2.96 2.96 0 0 1-1.635-3.257l.681-3.397Z"/>
                        <path d="M8.961 16a.93.93 0 0 0 .189-.019l3.4-.679a.961.961 0 0 0 .49-.263l6.118-6.117a2.884 2.884 0 0 0-4.079-4.078l-6.117 6.117a.96.96 0 0 0-.263.491l-.679 3.4A.961.961 0 0 0 8.961 16Zm7.477-9.8a.958.958 0 0 1 .68-.281.961.961 0 0 1 .682 1.644l-.315.315-1.36-1.36.313-.318Zm-5.911 5.911 4.236-4.236 1.359 1.359-4.236 4.237-1.7.339.341-1.699Z"/>
                    </svg>
                </div>
                <div>
                    <p class="text-3xl font-bold text-gray-800 dark:text-gray-100">{{ $ahsCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Analisa Harga Satuan</p>
                </div>
            </div>
        </div>

        <!-- Card Jumlah Item Pekerjaan -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-amber-600 dark:bg-amber-700 p-4">
                <h2 class="text-white text-lg font-semibold">Item Pekerjaan</h2>
            </div>
            <div class="p-6 flex items-center">
                <div class="p-3 bg-amber-100 dark:bg-amber-900/30 rounded-full mr-4">
                    <svg class="w-8 h-8 text-amber-600 dark:text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-3xl font-bold text-gray-800 dark:text-gray-100">{{ $itemPekerjaanCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total item pekerjaan</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Master -->
    <h2 class="text-xl font-bold mb-4 text-light-text dark:text-dark-text">Data Master</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Card Jumlah Upah -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-purple-600 dark:bg-purple-700 p-4">
                <h2 class="text-white text-lg font-semibold">Data Upah</h2>
            </div>
            <div class="p-6 flex items-center">
                <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full mr-4">
                    <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-3xl font-bold text-gray-800 dark:text-gray-100">{{ $upahCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total data upah</p>
                    <p class="text-sm font-medium mt-2">Rata-rata: Rp {{ number_format($avgUpahPrice, 0, ',', '.') }}</p>
                </div>
            </div>
        </div>

        <!-- Card Jumlah Bahan -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-teal-600 dark:bg-teal-700 p-4">
                <h2 class="text-white text-lg font-semibold">Data Bahan</h2>
            </div>
            <div class="p-6 flex items-center">
                <div class="p-3 bg-teal-100 dark:bg-teal-900/30 rounded-full mr-4">
                    <svg class="w-8 h-8 text-teal-600 dark:text-teal-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M17 5.923A1 1 0 0 0 16 5h-3V4a4 4 0 1 0-8 0v1H2a1 1 0 0 0-1 .923L.086 17.846A2 2 0 0 0 2.08 20h13.84a2 2 0 0 0 1.994-2.153L17 5.923ZM7 9a1 1 0 0 1-2 0V7h2v2Zm0-5a2 2 0 1 1 4 0v1H7V4Zm6 5a1 1 0 1 1-2 0V7h2v2Z"/>
                    </svg>
                </div>
                <div>
                    <p class="text-3xl font-bold text-gray-800 dark:text-gray-100">{{ $bahanCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total data bahan</p>
                    <p class="text-sm font-medium mt-2">Rata-rata: Rp {{ number_format($avgBahanPrice, 0, ',', '.') }}</p>
                </div>
            </div>
        </div>

        <!-- Card Jumlah Alat -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-red-600 dark:bg-red-700 p-4">
                <h2 class="text-white text-lg font-semibold">Data Alat</h2>
            </div>
            <div class="p-6 flex items-center">
                <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-full mr-4">
                    <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 7H7v6h6V7z" />
                        <path fill-rule="evenodd" d="M7 2a1 1 0 012 0v1h2V2a1 1 0 112 0v1h2a2 2 0 012 2v2h1a1 1 0 110 2h-1v2h1a1 1 0 110 2h-1v2a2 2 0 01-2 2h-2v1a1 1 0 11-2 0v-1H9v1a1 1 0 11-2 0v-1H5a2 2 0 01-2-2v-2H2a1 1 0 110-2h1V9H2a1 1 0 010-2h1V5a2 2 0 012-2h2V2zM5 5h10v10H5V5z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="text-3xl font-bold text-gray-800 dark:text-gray-100">{{ $alatCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total data alat</p>
                    <p class="text-sm font-medium mt-2">Rata-rata: Rp {{ number_format($avgAlatPrice, 0, ',', '.') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabel dan Grafik -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Tabel Customer Terbaru -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Customer Terbaru</h2>
            </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Nama</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Email</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tanggal Daftar</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse($latestCustomers as $customer)
                            <tr>
                                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-gray-200">{{ $customer->name }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{{ $customer->email }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{{ $customer->created_at->format('d M Y') }}</td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="3" class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">Tidak ada data customer</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tabel Proyek Terbaru -->
        <div class="bg-white dark:bg-dark-card rounded-xl shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-light-accent dark:bg-dark-accent p-4">
                <h2 class="text-white text-lg font-semibold">Proyek Terbaru</h2>
            </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Nama Proyek</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Lokasi</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Pemilik</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Tanggal</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse($latestProjects as $project)
                            <tr>
                                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-gray-200">{{ $project->name }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{{ $project->location }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{{ $project->user->name }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{{ $project->created_at->format('d M Y') }}</td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="4" class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">Tidak ada data proyek</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
