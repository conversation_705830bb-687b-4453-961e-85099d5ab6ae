@extends('layouts.app')

@section('content')
    @vite(['resources/css/time-schedule.css'])

    <div class="container mx-auto px-4 py-4">
        <!-- Hidden inputs untuk data penting -->
        @if (session('view_only'))
            <input type="hidden" id="viewOnly" value="1">
        @endif
        <input type="hidden" id="projectId" value="{{ $project->id }}">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <div class="flex items-center gap-4">
                    <button id="add-task" {{ !session('view_only') ? 'data-modal-target="tambahItemModal"' : '' }}
                        class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105 {{ session('view_only') ? 'locked-btn' : '' }}">
                        <i class="fas fa-plus-circle"></i> <PERSON><PERSON>
                    </button>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center mr-4">
                        <input type="checkbox" id="show-gantt"
                            class="mr-2 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            checked>
                        <label for="show-gantt" class="text-sm text-gray-700 dark:text-gray-300">Tampilkan Gantt
                            Chart</label>
                    </div>
                    <div class="flex items-center mr-4">
                        <input type="checkbox" id="show-scurve"
                            class="mr-2 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            checked>
                        <label for="show-scurve" class="text-sm text-gray-700 dark:text-gray-300">Tampilkan Kurva S</label>
                    </div>
                    <div class="flex items-center mr-4">
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">Skala:</span>
                        <select id="scale"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="day">Hari</option>
                            <option value="week" selected>Minggu</option>
                            {{-- <option value="month">Bulan</option> --}}
                        </select>
                    </div>
                    <button id="zoom-in"
                        class="bg-light-accent hover:bg-light-accent/80 text-white px-3 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                        <i class="fas fa-search-plus mr-1"></i>Zoom In
                    </button>
                    <button id="zoom-out"
                        class="bg-light-accent hover:bg-light-accent/80 text-white px-3 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                        <i class="fas fa-search-minus mr-1"></i>Zoom Out
                    </button>
                </div>
            </div>
        </div>

        <!-- Gantt chart & S-curve -->
        <div id="app"
            class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-md overflow-hidden transition-all duration-300">
            <!-- Body content untuk Gantt chart -->
            <div class="gantt-body">
                <!-- Left container for headers and grid -->
                <div id="left-container" class="left-container">
                    <!-- Header for grid -->
                    <div class="gantt-header">
                        <div class="gantt-grid">
                            <div class="header-row">
                                <div class="header-cell" style="width: 300px">Item Pekerjaan</div>
                                <div class="header-cell" style="width: 100px">Harga</div>
                                <div class="header-cell" style="width: 100px">Durasi</div>
                                <div class="header-cell" style="width: 100px">Bobot (%)</div>
                                <div class="header-cell" style="width: 50px">Aksi</div>
                            </div>
                        </div>
                    </div>

                    <!-- Grid below header -->
                    <div id="grid">
                        <chart>
                            <chart-body id="grid-body">
                                <!-- Total row will be added here dynamically -->
                            </chart-body>
                        </chart>
                        <div class="grid-resizer" id="grid-resizer" title="Resize grid"></div>
                    </div>
                </div>

                <!-- Scrollable container that contains both timeline and chart -->
                <div id="scrollable-container" class="scrollable-container">
                    <!-- Timeline moved here from header -->
                    <div class="timeline-container">
                        <div class="timeline-month"></div>
                        <div class="timeline-day"></div>
                    </div>

                    <!-- Chart container -->
                    <div id="chart-container">
                        <div class="grid-lines" id="grid-lines"></div>
                        <div class="tasks" id="tasks"></div>
                        <canvas class="scurve" id="scurve"></canvas>
                    </div>
                </div>
            </div>
            <!-- Resize handle for vertical resizing -->
            <div class="resize-handle" id="resize-handle" title="Resize vertically"></div>
        </div>
    </div>

    <!-- Hidden div for rab.js to prevent errors -->
    <div class="hidden">
        <table>
            <tbody></tbody>
        </table>
    </div>

    <!-- Modal Tambah Item Pekerjaan -->
    <div id="tambahItemModal"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div
            class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-md relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
            <!-- Header -->
            <div
                class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <div class="flex justify-between items-center">
                    <h2 class="text-white dark:text-dark-text text-lg font-semibold">Tambah Item Pekerjaan</h2>
                    <button type="button"
                        onclick="document.getElementById('tambahItemModal').classList.add('hidden'); document.getElementById('tambahItemModal').style.display = 'none';"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto"
                style="scrollbar-width: thin;">
                <form class="space-y-4" id="addItemForm">
                    <div>
                        <label for="itemSelect" class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Item
                            Pekerjaan</label>
                        <select id="itemSelect"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                            required>
                            <option value="" selected disabled>Pilih Item Pekerjaan</option>
                            @if (isset($kategoriPekerjaans))
                                @foreach ($kategoriPekerjaans as $kategori)
                                    <optgroup label="{{ $kategori->nama_kategori }}">
                                        @foreach ($kategori->items as $item)
                                            @php
                                                $used = isset($itemBobotTersisa[$item->id]['used_bobot'])
                                                    ? $itemBobotTersisa[$item->id]['used_bobot']
                                                    : 0;
                                                $remaining = isset($itemBobotTersisa[$item->id]['remaining_bobot'])
                                                    ? $itemBobotTersisa[$item->id]['remaining_bobot']
                                                    : 0;
                                                $total = isset($itemBobotTersisa[$item->id]['total_bobot'])
                                                    ? $itemBobotTersisa[$item->id]['total_bobot']
                                                    : 0;
                                                $disabled = $remaining <= 0 ? 'disabled' : '';
                                                $label =
                                                    $remaining <= 0
                                                        ? ' (Bobot Habis)'
                                                        : ($used > 0
                                                            ? ' (Tersisa: ' . $remaining . '%)'
                                                            : '');
                                            @endphp
                                            <option value="{{ $item->id }}" data-price="{{ $item->harga_total }}"
                                                data-bobot="{{ $remaining }}" {{ $disabled }}>
                                                {{ $item->uraian_item . $label }}
                                            </option>
                                        @endforeach
                                    </optgroup>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div>
                        <label for="itemPrice"
                            class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Harga</label>
                        <input type="text" id="itemPrice"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                            placeholder="Harga akan terisi otomatis" readonly>
                    </div>
                    <div>
                        <label for="itemWeight"
                            class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Bobot
                            (%)</label>
                        <input type="number" id="itemWeight" step="0.01"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                            placeholder="Bobot akan terisi otomatis" readonly>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="startDate"
                                class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Tanggal
                                Mulai</label>
                            <input type="date" id="startDate"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>
                        <div>
                            <label for="duration"
                                class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Durasi
                                (Hari)</label>
                            <input type="number" id="duration" min="1"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                placeholder="Durasi" required>
                        </div>
                    </div>
                    <div class="mt-6 border-t dark:border-gray-700 pt-4 flex justify-end space-x-3">
                        <button type="submit"
                            class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                            <i class="fas fa-plus-circle mr-1"></i>Tambah Item
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Edit Item Pekerjaan -->
    <div id="editItemModal"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div
            class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-md relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
            <!-- Header -->
            <div
                class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <div class="flex justify-between items-center">
                    <h2 class="text-white dark:text-dark-text text-lg font-semibold">Edit Item Pekerjaan</h2>
                    <button type="button"
                        onclick="document.getElementById('editItemModal').classList.add('hidden'); document.getElementById('editItemModal').style.display = 'none';"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto"
                style="scrollbar-width: thin;">
                <form class="space-y-4" id="editItemForm">
                    <input type="hidden" id="editTaskId">
                    <div>
                        <label for="editItemSelect"
                            class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Item Pekerjaan</label>
                        <select id="editItemSelect"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                            required>
                            <option value="" selected disabled>Pilih Item Pekerjaan</option>
                            @if (isset($kategoriPekerjaans))
                                @foreach ($kategoriPekerjaans as $kategori)
                                    <optgroup label="{{ $kategori->nama_kategori }}">
                                        @foreach ($kategori->items as $item)
                                            @php
                                                $used = isset($itemBobotTersisa[$item->id]['used_bobot'])
                                                    ? $itemBobotTersisa[$item->id]['used_bobot']
                                                    : 0;
                                                $remaining = isset($itemBobotTersisa[$item->id]['remaining_bobot'])
                                                    ? $itemBobotTersisa[$item->id]['remaining_bobot']
                                                    : 0;
                                                $total = isset($itemBobotTersisa[$item->id]['total_bobot'])
                                                    ? $itemBobotTersisa[$item->id]['total_bobot']
                                                    : 0;
                                                $disabled = $remaining <= 0 ? 'disabled' : '';
                                                $label =
                                                    $remaining <= 0
                                                        ? ' (Bobot Habis)'
                                                        : ($used > 0
                                                            ? ' (Tersisa: ' . $remaining . '%)'
                                                            : '');
                                            @endphp
                                            <option value="{{ $item->id }}" data-price="{{ $item->harga_total }}"
                                                data-bobot="{{ $remaining }}" {{ $disabled }}>
                                                {{ $item->uraian_item . $label }}
                                            </option>
                                        @endforeach
                                    </optgroup>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div>
                        <label for="editItemPrice"
                            class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Harga</label>
                        <input type="text" id="editItemPrice"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                            placeholder="Harga akan terisi otomatis" readonly>
                    </div>
                    <div>
                        <label for="editItemWeight"
                            class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Bobot (%)</label>
                        <input type="number" id="editItemWeight" step="0.01"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                            placeholder="Bobot akan terisi otomatis" readonly>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="editStartDate"
                                class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Tanggal
                                Mulai</label>
                            <input type="date" id="editStartDate"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                required>
                        </div>
                        <div>
                            <label for="editDuration"
                                class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Durasi
                                (Hari)</label>
                            <input type="number" id="editDuration" min="1"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
                                placeholder="Durasi" required>
                        </div>
                    </div>
                    <div class="mt-6 border-t dark:border-gray-700 pt-4 flex justify-end space-x-3">
                        <button type="submit"
                            class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                            <i class="fas fa-save mr-1"></i>Simpan Item
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Tambahkan hidden input untuk menyimpan data dari server -->
    <input type="hidden" id="projectId" value="{{ $project->id ?? '' }}">
    <input type="hidden" id="projectIdInput" value="{{ $project->id ?? '' }}">

    @vite(['resources/js/time-schedule-gantt.js', 'resources/js/ahsp.js', 'resources/js/rab.js', 'resources/js/volume.js', 'resources/js/ahsrab.js', 'resources/js/pdf-export.js', 'resources/js/preview-download-modal.js'])
@endsection
