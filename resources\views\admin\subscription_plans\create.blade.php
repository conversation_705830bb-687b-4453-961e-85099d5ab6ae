@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-light-text dark:text-dark-text mb-2">Tambah Paket Langganan</h1>
                <p class="text-gray-600 dark:text-gray-400">Buat paket langganan baru untuk pelanggan</p>
            </div>
            <div>
                <a href="{{ route('admin.subscription-plans.index') }}"
                    class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-150">
                    <i class="fas fa-arrow-left mr-1"></i> Kembali
                </a>
            </div>
        </div>

        @if ($errors->any())
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded dark:bg-red-900/30 dark:text-red-400"
                role="alert">
                <ul class="list-disc pl-4">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Form Tambah Paket -->
        <div class="bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-200">
            <div class="bg-blue-600 dark:bg-blue-700 p-4">
                <h2 class="text-white text-lg font-semibold">Form Tambah Paket</h2>
            </div>

            <div class="p-6">
                <form action="{{ route('admin.subscription-plans.store') }}" method="POST">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nama Paket <span
                                    class="text-red-500">*</span></label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                                required>
                        </div>

                        <div>
                            <label for="slug"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Slug <span
                                    class="text-red-500">*</span></label>
                            <input type="text" id="slug" name="slug" value="{{ old('slug') }}"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                                required>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Slug harus unik dan hanya berisi huruf
                                kecil, angka, dan tanda hubung.</p>
                        </div>

                        <div>
                            <label for="price"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Harga (Rp) <span
                                    class="text-red-500">*</span></label>
                            <input type="number" id="price" name="price" value="{{ old('price') }}" min="0"
                                step="1000"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                                required>
                        </div>

                        <div>
                            <label for="duration_days"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Durasi (Hari) <span
                                    class="text-red-500">*</span></label>
                            <input type="number" id="duration_days" name="duration_days"
                                value="{{ old('duration_days', 30) }}" min="1"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                                required>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Contoh: 30 hari (1 bulan), 90 hari (3
                                bulan), 365 hari (1 tahun)</p>
                        </div>

                        <div>
                            <label for="project_limit"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batas Jumlah Proyek
                                <span class="text-red-500">*</span></label>
                            <input type="number" id="project_limit" name="project_limit"
                                value="{{ old('project_limit', 1) }}" min="1"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                                required>
                        </div>

                        <div>
                            <label for="max_users"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batas Jumlah
                                Pengguna <span class="text-red-500">*</span></label>
                            <input type="number" id="max_users" name="max_users" value="{{ old('max_users', 1) }}"
                                min="1"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                                required>
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="description"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Deskripsi</label>
                        <textarea id="description" name="description" rows="3"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white">{{ old('description') }}</textarea>
                    </div>

                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fitur</label>

                        <div id="features-container" class="space-y-3">
                            <div class="flex items-center">
                                <input type="text" name="features[]"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                                    placeholder="Contoh: Akses ke semua fitur">
                                <button type="button"
                                    class="ml-2 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-50 dark:hover:bg-red-900/10 transition-all duration-300"
                                    onclick="removeFeature(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <button type="button"
                            class="mt-2 text-light-accent dark:text-dark-accent hover:text-light-accent/80 dark:hover:text-dark-accent/80 text-sm font-medium flex items-center transition-all duration-300 transform hover:translate-x-1"
                            onclick="addFeature()">
                            <i class="fas fa-plus-circle mr-2"></i> Tambah Fitur
                        </button>
                    </div>

                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">Pengaturan Paket</h3>

                        <div class="space-y-3 mb-6">
                            <!-- Tambahkan hidden input untuk memastikan nilai boolean selalu dikirim -->
                            <input type="hidden" name="is_active" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('is_active') ? 'checked' : 'checked' }}>
                                <label for="is_active"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Aktif</label>
                            </div>

                            <!-- Tambahkan hidden input untuk memastikan nilai boolean selalu dikirim -->
                            <input type="hidden" name="is_featured" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_featured" name="is_featured" value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('is_featured') ? 'checked' : '' }}>
                                <label for="is_featured"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Paket
                                    Unggulan</label>
                            </div>
                        </div>

                        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">Akses Fitur</h3>

                        <div class="space-y-3">
                            <!-- Export Excel -->
                            <input type="hidden" name="can_export_excel" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="can_export_excel" name="can_export_excel" value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('can_export_excel') ? 'checked' : '' }}>
                                <label for="can_export_excel"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Export Excel</label>
                            </div>

                            <!-- Export Excel dengan Rumus -->
                            <input type="hidden" name="can_export_excel_formula" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="can_export_excel_formula" name="can_export_excel_formula"
                                    value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('can_export_excel_formula') ? 'checked' : '' }}>
                                <label for="can_export_excel_formula"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Export Excel dengan
                                    Rumus</label>
                            </div>

                            <!-- Export PDF -->
                            <input type="hidden" name="can_export_pdf" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="can_export_pdf" name="can_export_pdf" value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('can_export_pdf') ? 'checked' : '' }}>
                                <label for="can_export_pdf"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Export PDF</label>
                            </div>

                            <!-- Time Schedule -->
                            <input type="hidden" name="can_use_time_schedule" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="can_use_time_schedule" name="can_use_time_schedule"
                                    value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('can_use_time_schedule') ? 'checked' : '' }}>
                                <label for="can_use_time_schedule"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Fitur Time
                                    Schedule</label>
                            </div>

                            <!-- AHSP Empiris -->
                            <input type="hidden" name="can_use_empirical_ahsp" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="can_use_empirical_ahsp" name="can_use_empirical_ahsp"
                                    value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('can_use_empirical_ahsp') ? 'checked' : '' }}>
                                <label for="can_use_empirical_ahsp"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Fitur AHSP
                                    Empiris</label>
                            </div>

                            <!-- Fitur Progres -->
                            <input type="hidden" name="can_use_progress" value="0">
                            <div class="flex items-center">
                                <input type="checkbox" id="can_use_progress" name="can_use_progress"
                                    value="1"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                                    {{ old('can_use_progress', true) ? 'checked' : '' }}>
                                <label for="can_use_progress"
                                    class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Fitur Progres</label>
                            </div>
                        </div>
                    </div>

                    <!-- Durasi Berlangganan -->
                    <div class="mt-6">
                        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">Durasi Berlangganan</h3>
                        <div
                            class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Tambahkan opsi durasi berlangganan dengan diskon atau bulan gratis. Contoh: "Berlangganan 6
                                bulan, gratis 1 bulan" atau "Berlangganan 12 bulan, diskon 20%"
                            </p>

                            <div id="durations-container" class="space-y-4">
                                <!-- Durasi akan ditambahkan di sini -->
                            </div>

                            <button type="button"
                                class="mt-4 text-light-accent dark:text-dark-accent hover:text-light-accent/80 dark:hover:text-dark-accent/80 text-sm font-medium flex items-center transition-all duration-300 transform hover:translate-x-1"
                                onclick="addDuration()">
                                <i class="fas fa-plus-circle mr-2"></i> Tambah Durasi
                            </button>
                        </div>
                    </div>

                    <div class="mt-8 flex justify-end">
                        <button type="submit"
                            class="bg-light-accent hover:bg-light-accent/80 text-white px-6 py-2.5 rounded-lg transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105 font-medium">
                            <i class="fas fa-save mr-2"></i> Simpan Paket
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function addFeature() {
            const container = document.getElementById('features-container');
            const featureDiv = document.createElement('div');
            featureDiv.className = 'flex items-center';
            featureDiv.innerHTML = `
            <input type="text" name="features[]" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white" placeholder="Contoh: Akses ke semua fitur">
            <button type="button" class="ml-2 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-50 dark:hover:bg-red-900/10 transition-all duration-300" onclick="removeFeature(this)">
                <i class="fas fa-times"></i>
            </button>
        `;
            container.appendChild(featureDiv);
        }

        function removeFeature(button) {
            const featureDiv = button.parentElement;
            featureDiv.remove();
        }

        function addDuration() {
            const container = document.getElementById('durations-container');
            const durationDiv = document.createElement('div');
            durationDiv.className = 'duration-item bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm';

            // Generate a unique temporary ID for new durations
            const tempId = 'new_' + Date.now();

            durationDiv.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Durasi (bulan)</label>
                        <input type="number" name="duration_months[]" value="1" min="1" max="60"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white duration-months" onchange="updateDescription(this)">
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Jenis Promosi</label>
                        <select name="promo_type[]"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white promo-type" onchange="updateDescription(this)">
                            <option value="discount">Diskon (%)</option>
                            <option value="free_months">Bulan Gratis</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Nilai Promosi</label>
                        <input type="number" name="promo_value[]" value="10" min="1" max="100"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white promo-value" onchange="updateDescription(this)">
                    </div>
                </div>
                <div class="mt-3 flex justify-between items-center">
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        <span class="duration-description">Diskon 10% untuk langganan 1 bulan</span>
                    </div>
                    <button type="button"
                        class="text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300 p-1 rounded-full hover:bg-red-50 dark:hover:bg-red-900/10 transition-all duration-300"
                        onclick="removeDuration(this)">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
                <input type="hidden" name="duration_id[]" value="${tempId}">
                <input type="hidden" name="duration_is_active[]" value="1">
                <input type="hidden" name="duration_sort_order[]" value="0">
            `;

            container.appendChild(durationDiv);
            updateDescription(durationDiv.querySelector('.duration-months'));
        }

        function removeDuration(button) {
            const durationDiv = button.closest('.duration-item');
            durationDiv.remove();
        }

        function updateDescription(element) {
            const durationDiv = element.closest('.duration-item');
            const durationMonths = durationDiv.querySelector('.duration-months').value;
            const promoType = durationDiv.querySelector('.promo-type').value;
            const promoValue = durationDiv.querySelector('.promo-value').value;
            const descriptionSpan = durationDiv.querySelector('.duration-description');

            if (promoType === 'discount') {
                descriptionSpan.textContent = `Diskon ${promoValue}% untuk langganan ${durationMonths} bulan`;
            } else {
                descriptionSpan.textContent = `Gratis ${promoValue} bulan untuk langganan ${durationMonths} bulan`;
            }
        }

        // Auto-generate slug from name
        document.getElementById('name').addEventListener('input', function() {
            const name = this.value;
            const slug = name.toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-');
            document.getElementById('slug').value = slug;
        });
    </script>
@endsection
