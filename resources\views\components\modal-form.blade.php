@props(['id', 'title', 'action', 'module'])

<div id="{{ $id }}" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50">
  <div class="bg-white dark:bg-dark-card p-4 rounded-xl shadow-lg w-full max-w-3xl relative border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <button type="button" onclick="closeModal('{{ $id }}')" class="absolute top-2 right-2 bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-gray-700 dark:text-gray-300 rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    <h2 class="text-xl font-bold mb-4">{{ $title }}</h2>
    <form method="POST" action="{{ $action }}">
      @csrf
      <div class="space-y-4">
        <div>
          <label class="block font-medium">Uraian</label>
          <input type="text" name="uraian" class="w-full border rounded p-2" required>
        </div>
        <div>
          <label class="block font-medium">Satuan</label>
          <input type="text" name="satuan" class="w-full border rounded p-2" required>
        </div>
        <div>
          <label class="block font-medium">Harga</label>
          <input type="number" step="0.01" name="harga" class="w-full border rounded p-2" required>
        </div>
        <input type="hidden" name="sumber" value="{{ auth()->user()->name }}">
        <input type="hidden" name="alamat" value="{{ auth()->user()->currentProject->city ?? '' }}">
        <input type="hidden" name="project_id" value="{{ auth()->user()->currentProject->id ?? '' }}">
      </div>
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" onclick="closeModal('{{ $id }}')" class="bg-gray-500 text-white px-4 py-2 rounded">
          Batal
        </button>
        <x-loading-button type="submit" color="blue" id="submitBtn{{ $id }}">
          Simpan
        </x-loading-button>
      </div>
    </form>
  </div>
</div>