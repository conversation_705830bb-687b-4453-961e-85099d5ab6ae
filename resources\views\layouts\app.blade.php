<!DOCTYPE html>
<html lang="en" id="html-root">

<head>
    <!-- Early theme initialization to prevent FOUC (Flash of Unstyled Content) -->
    <script>
        // Check for saved theme preference or use system preference
        const savedTheme = localStorage.getItem("theme");
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;

        // Apply theme immediately before page renders
        if (savedTheme === "dark" || (!savedTheme && prefersDark)) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Estimator App</title>

    <!-- Tailwind CSS -->
    @vite('resources/css/app.css')
    @vite('resources/css/loading.css')

    <!-- Flowbite CSS -->
    <link href="{{ asset('css/flowbite.min.css') }}" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ asset('css/all.min.css') }}">

    <!-- Toastify CSS and JS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- Tampilkan loading pada awal halaman dimuat -->
    <script>
        // Check if this is a pagination navigation
        function isPaginationNavigation() {
            const isPagination = localStorage.getItem('isPaginationNavigation') === 'true';
            const timestamp = parseInt(localStorage.getItem('paginationTimestamp') || '0');
            const now = Date.now();
            const isRecent = (now - timestamp) < 5000; // Within 5 seconds

            // Don't clear the flag here, we'll do it after we've used it
            return isPagination && isRecent;
        }

        // Apply pagination navigation class to html element
        if (isPaginationNavigation()) {
            document.documentElement.classList.add('pagination-navigation');
            console.log('Pagination navigation detected - added pagination-navigation class');

            // Clear the flag after we've used it
            setTimeout(function() {
                localStorage.removeItem('isPaginationNavigation');
                localStorage.removeItem('paginationTimestamp');

                // Remove the class after a delay to ensure animations complete
                setTimeout(function() {
                    document.documentElement.classList.remove('pagination-navigation');
                }, 1000);
            }, 1000);
        }

        // Show loading when page starts loading
        document.addEventListener('DOMContentLoaded', function() {
            // Skip loading animation for pagination
            if (document.documentElement.classList.contains('pagination-navigation')) {
                console.log('Pagination navigation detected - skipping loading animation');

                // Hide preloader immediately
                const preloader = document.getElementById('preloader');
                if (preloader) {
                    preloader.style.opacity = '0';
                    preloader.style.display = 'none';
                }

                // Hide loading overlay immediately
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                }

                return;
            }

            // Fungsi untuk memeriksa apakah halaman saat ini adalah halaman login atau register
            function isAuthPage() {
                const currentPath = window.location.pathname;
                return currentPath.includes('/login') ||
                    currentPath.includes('/register') ||
                    currentPath === '/' || // Jika root redirect ke login
                    currentPath.includes('/password/reset');
            }

            // Hanya tampilkan loading pada halaman login dan register
            if (window.showLoading && isAuthPage()) {
                window.showLoading();
                window.startProgress();
            }

            // Sembunyikan preloader setelah halaman dimuat
            const preloader = document.getElementById('preloader');
            if (preloader) {
                setTimeout(function() {
                    preloader.style.opacity = '0';
                    setTimeout(function() {
                        preloader.style.display = 'none';
                    }, 500);
                }, 500);
            }
        });
    </script>
</head>

<body class="bg-light-bg text-light-text dark:bg-dark-bg dark:text-dark-text transition-colors duration-200">
    <!-- Preloader - muncul sebelum JS dimuat (hanya pada halaman login dan register) -->
    <div id="preloader" style="display: none;">
        <div class="chart-loading-container">
            <div class="loading-brand">Estimator</div>

            <!-- Bar Chart Animation -->
            <div class="bar-chart">
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-baseline"></div>
            </div>

            <div class="loading-text">Memuat data</div>
            <div class="loading-progress"></div>
        </div>
    </div>

    <script>
        // Tampilkan preloader hanya pada halaman login dan register
        (function() {
            const currentPath = window.location.pathname;
            const isAuthPage = currentPath.includes('/login') ||
                currentPath.includes('/register') ||
                currentPath === '/' ||
                currentPath.includes('/password/reset');

            if (isAuthPage) {
                document.getElementById('preloader').style.display = 'block';
            }
        })();
    </script>

    <x-navbar />

    <div class="p-4 sm:ml-64">
        <div class="p-4 mt-14">
            @yield('content')
        </div>
    </div>

    <!-- Profile Modal -->
    <x-profile-modal />

    <!-- Loading Overlay with Animated Spinner (hanya pada halaman login dan register) -->
    <div id="loading-overlay" class="fixed inset-0 flex items-center justify-center z-50 hidden">
        <!-- Loading Particles Background -->
        <div class="loading-particles" id="loading-particles"></div>

        <div class="chart-loading-container">
            <div class="loading-brand">Estimator</div>

            <!-- Bar Chart Animation -->
            <div class="bar-chart">
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-bar"></div>
                <div class="chart-baseline"></div>
            </div>

            <div class="loading-text">Sedang memproses</div>
            <div class="loading-progress"></div>
        </div>
    </div>

    <script>
        // Pastikan loading overlay tidak muncul pada halaman selain login dan register
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const isAuthPage = currentPath.includes('/login') ||
                currentPath.includes('/register') ||
                currentPath === '/' ||
                currentPath.includes('/password/reset');

            if (!isAuthPage) {
                // Pastikan loading overlay tidak muncul
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                    // Tambahkan class tambahan untuk memastikan tidak muncul
                    loadingOverlay.style.display = 'none';
                }

                // Pastikan progress bar tidak muncul
                const progressBar = document.getElementById('progress-bar');
                if (progressBar) {
                    progressBar.style.width = '0%';
                    progressBar.style.display = 'none';
                }
            }
        });
    </script>

    <!-- Progress Bar -->
    <div id="progress-bar" class="fixed top-0 left-0 z-50" style="width: 0%;"></div>

    @vite(['resources/js/app.js'])

    <!-- Flowbite JS -->
    <script src="{{ asset('js/flowbite.min.js') }}"></script>

    <!-- Core JS Files -->
    <script>
        // Define global variables needed by our modal and detail scripts
        window.csrfToken = "{{ csrf_token() }}";
        window.upahItems = [];
        window.alatItems = [];
        window.bahanItems = [];
    </script>
    @vite(['resources/js/modal.js', 'resources/js/detail-modal.js', 'resources/js/loading.js', 'resources/js/notifications.js', 'resources/js/confirm.js', 'resources/js/pagination.js', 'resources/js/ajax-pagination.js', 'resources/js/profile.js', 'resources/js/subscription-check.js', 'resources/js/subscription-ajax-handler.js'])

    <script>
        // Global function for opening resource modals
        function openTambahModal() {
            const selectedCategory = document.getElementById('selectedCategoryLabel')?.textContent.trim() || '';
            console.log('Opening modal for category:', selectedCategory);

            try {
                switch (selectedCategory) {
                    case 'Upah':
                        window.openUpahAHSModal();
                        break;
                    case 'Alat':
                        window.openAlatAHSModal();
                        break;
                    case 'Bahan':
                        window.openBahanAHSModal();
                        break;
                    default:
                        console.error('Invalid category:', selectedCategory);
                }
            } catch (e) {
                console.error('Error opening modal:', e);
                alert('Terjadi kesalahan saat membuka modal. Silakan coba lagi.');
            }
        }
    </script>

    <!-- Handle subscription error flash messages -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if(session('subscription_error'))
                if (typeof window.showInfoToast === 'function') {
                    window.showInfoToast(
                        {!! json_encode(session('subscription_error')) !!},
                        'Fitur Terbatas',
                        null,
                        7000,
                        {!! json_encode(session('upgrade_url', route('subscriptions.index'))) !!}
                    );
                }
            @endif
        });
    </script>

    @yield('scripts')
    @include('components.modal-alert')
    @include('components.resource-modals')

    <!-- Toast Notifications Container -->
    <div id="toast-container" class="toast-container"></div>
</body>

</html>
