import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";

export default defineConfig({
    plugins: [
        laravel({
            input: [
                "resources/css/app.css",
                "resources/css/loading.css",
                "resources/css/pulse-animation.css",
                "resources/css/pagination.css",
                "resources/css/time-schedule.css",
                "resources/js/app.js",
                "resources/js/modal.js",
                "resources/js/ahsp.js",
                "resources/js/rab.js",
                "resources/js/detail-modal.js",
                "resources/js/volume.js",
                "resources/js/loading.js",
                "resources/js/notifications.js",
                "resources/js/confirm.js",
                "resources/js/ahsrab.js",
                "resources/js/pagination.js",
                "resources/js/ajax-pagination.js",
                "resources/js/customer.js",
                "resources/js/profile.js",
                "resources/js/time-schedule-gantt.js",
                "resources/js/preview-download-modal.js",
                "resources/js/table-sort.js",
                "resources/js/payment-gateway.js",
                "resources/js/progres.js",
            ],
            refresh: true,
        }),
    ],
});
