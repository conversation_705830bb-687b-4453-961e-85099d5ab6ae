@extends('layouts.auth')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-light-bg dark:bg-dark-bg transition-colors duration-200">
    <div class="max-w-md w-full space-y-8 p-8 bg-white dark:bg-dark-card rounded-lg shadow-lg transition-colors duration-200">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                Atur Ulang Kata Sandi
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Masukkan alamat email Anda dan kami akan mengirimkan link untuk mengatur ulang kata sandi Anda.
            </p>
        </div>
        <form class="mt-8 space-y-6" action="{{ route('password.email') }}" method="POST">
            @csrf
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="email" class="sr-only"><PERSON><PERSON><PERSON> Email</label>
                    <input id="email" name="email" type="email" required
                        class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 dark:border-dark-border placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-dark-bg-secondary focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm transition-colors duration-200"
                        placeholder="Alamat Email">
                </div>
            </div>

            @if (session('status'))
                <div class="bg-green-100 dark:bg-blue-900 border border-green-400 text-green-700 dark:text-blue-300 px-4 py-3 rounded relative">
                    {{ session('status') }}
                </div>
            @endif

            @if($errors->any())
                <div class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 dark:text-red-300 px-4 py-3 rounded relative">
                    {{ $errors->first() }}
                </div>
            @endif

            <div>
                <button type="submit"
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Kirim Link Atur Ulang Kata Sandi
                </button>
            </div>
        </form>
        <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">
                Ingat kata sandi Anda?
                <a href="{{ route('login') }}" class="font-medium text-indigo-600 dark:text-blue-400 hover:text-indigo-500 dark:hover:text-blue-300 transition-colors duration-200">
                    Masuk di sini
                </a>
            </p>
        </div>
    </div>
</div>
@endsection