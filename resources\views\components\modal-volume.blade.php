<!-- Modal rabVolumeModal -->
<div id="rabVolumeModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h1 class="text-white dark:text-dark-text text-lg font-semibold">Perhitungan Volume</h1>
        <button type="button" onclick="closeVolumeModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
      <!-- Total Hasil -->
      <div class="border-t dark:border-dark-border flex justify-center mb-2">
        <span class="font-bold mr-2 dark:text-gray-200">Total Volume</span>
      </div>
      <div class="flex justify-center mb-4">
        <span class="py-2 px-2 font-bold text-2xl text-light-accent dark:text-dark-accent grand-total-label">
          <span id="totalHasil"></span>
          <span id="unitDisplay"></span>
        </span>
      </div>
      
        <div class="overflow-x-auto max-h-[520px]">
        <table class="min-w-full border dark:border-dark-border">
          <thead id="volumeTableHeader" class="bg-blue-200 dark:bg-dark-accent/30 sticky top-0 z-10">
              <!-- Header tabel akan dihasilkan oleh JavaScript -->
            </thead>
            <tbody id="volumeTableBody">
              <!-- Data volume akan diisi oleh JavaScript -->
            </tbody>
          </table>
        </div>
      
      <div class="mt-6 border-t dark:border-dark-border pt-4 flex justify-center">
        <button type="button" onclick="closeVolumeModal()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-check-circle mr-1"></i> Selesai
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal inputVolumeModal untuk input data volume calculation -->
<div id="inputVolumeModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 id="inputVolumeModalTitle" class="text-white dark:text-dark-text text-lg font-semibold">Input Perhitungan Volume</h2>
        <button type="button" onclick="closeInputVolumeModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <form id="volumeForm">
      <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
        <div id="volumeFormContainer">
        <!-- Form input akan di-generate secara dinamis sesuai satuan -->
      </div>
        
        <div class="mt-6 border-t dark:border-dark-border pt-4 flex justify-end space-x-3">
          <button type="button" onclick="closeInputVolumeModal()" class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow">
          <i class="fas fa-times-circle mr-1"></i> Batal
        </button>
          <button type="submit" id="btnSaveVolume" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-save mr-1"></i> Simpan
        </button>
        </div>
      </div>
    </form>
  </div>
  </div>

<!-- Context Menu untuk Volume Calculation -->
<div id="volume-context-menu" class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-32 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
  <button onclick="handleVolumeEdit()" class="w-full px-4 py-2 text-left text-blue-500 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-dark-accent/20 hover:text-blue-900 text-sm flex items-center edit-btn">
    <i class="fas fa-edit mr-2 w-4 h-4"></i> Edit
  </button>
  <button onclick="handleVolumeDelete()" class="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-dark-accent/20 text-sm flex items-center hover:text-red-900 text-red-500 delete-btn">
    <i class="fas fa-trash-alt mr-2 w-4 h-4"></i> Hapus
  </button>
</div>