// Ensure currentAhsId is set from URL if not already set
document.addEventListener("DOMContentLoaded", function () {
    // If window.currentAhsId is not set yet, try to get it from the URL
    if (!window.currentAhsId) {
        const urlParts = window.location.pathname.split("/");
        const ahsIdIndex = urlParts.indexOf("ahs") + 1;
        if (ahsIdIndex > 0 && ahsIdIndex < urlParts.length) {
            const possibleId = urlParts[ahsIdIndex];
            // Check if it's a valid ID (number)
            if (!isNaN(possibleId) && possibleId.trim() !== "") {
                window.currentAhsId = parseInt(possibleId);
                console.log("Set currentAhsId from URL:", window.currentAhsId);
            }
        }
    }
});

// Make the function globally accessible
window.openTambahModal = function () {
    const selectedCategory = document
        .getElementById("selectedCategoryLabel")
        .textContent.trim();
    switch (selectedCategory) {
        case "Upah":
            openUpahAHSModal();
            break;
        case "Alat":
            openAlatAHSModal();
            break;
        case "Bahan":
            openBahanAHSModal();
            break;
        default:
            console.error("Kategori tidak valid");
    }
};

// Global variables to store items data
window.upahItems = window.upahItems || [];
window.alatItems = window.alatItems || [];
window.bahanItems = window.bahanItems || [];
window.currentDetailCategory = null;

// Function to open detail modal and load data
window.openDetailModal = function (category) {
    console.log("Opening detail modal for category:", category);
    const modal = document.getElementById("detailModal");
    const categoryLabel = document.getElementById("selectedCategoryLabel");

    if (!modal || !categoryLabel) {
        console.error("Detail modal elements not found");
        return;
    }

    // Set the category label
    categoryLabel.textContent = category;
    window.currentDetailCategory = category;

    // Show the modal
    modal.classList.remove("hidden");

    // Load data for the selected category
    loadResourceData(category);
};

// Function to close detail modal
window.closeDetailModal = function () {
    const modal = document.getElementById("detailModal");
    if (modal) {
        modal.classList.add("hidden");
    }
};

// Function to load resource data via AJAX
function loadResourceData(category) {
    console.log("Loading resource data for:", category);
    const url = "/" + category.toLowerCase();

    // Show loading indicator or message
    const tableBody = document.getElementById("masterTableBody");
    if (tableBody) {
        tableBody.innerHTML =
            '<tr><td colspan="7" class="text-center p-4">Loading data...</td></tr>';
    }

    // Use fetch to get the data
    fetch(url + "/data", {
        method: "GET",
        headers: {
            Accept: "application/json",
            "X-Requested-With": "XMLHttpRequest",
        },
    })
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((data) => {
            console.log("Data loaded:", data);
            // Store the data in the appropriate global variable
            if (category === "Upah") window.upahItems = data;
            else if (category === "Alat") window.alatItems = data;
            else if (category === "Bahan" && data.length > 0)
                window.bahanItems = data;

            // Populate the table with the data
            populateMasterTable(category);
        })
        .catch((error) => {
            console.error("Error loading data:", error);
            if (tableBody) {
                tableBody.innerHTML =
                    '<tr><td colspan="7" class="text-center p-4 text-red-500">Error loading data. Please try again.</td></tr>';
            }
        });
}

// Function to populate the master table based on category
window.populateMasterTable = function (category) {
    const tableBody = document.getElementById("masterTableBody");
    if (!tableBody) return;

    // Clear the table first
    tableBody.innerHTML = "";

    // Get the appropriate items array based on category
    let items = [];
    if (category === "Upah" && window.upahItems) items = window.upahItems;
    else if (category === "Alat" && window.alatItems) items = window.alatItems;
    else if (category === "Bahan" && window.bahanItems)
        items = window.bahanItems;

    // Store current category for reference
    window.currentDetailCategory = category;

    // Populate the table with items
    items.forEach((item, index) => {
        const row = document.createElement("tr");
        row.className = index % 2 === 0 ? "bg-white" : "bg-gray-50";

        // Get the correct property names based on category
        const uraianField =
            category === "Upah"
                ? "uraian_tenaga"
                : category === "Alat"
                ? "uraian_alat"
                : "uraian_bahan";
        const hargaField =
            category === "Upah"
                ? "harga"
                : category === "Alat"
                ? "harga_alat"
                : "harga_bahan";

        // Format the row HTML
        row.innerHTML = `
            <td class="p-2 border text-center">
                <input type="checkbox" name="selected_items" value="${
                    item.id
                }" data-category="${category.toLowerCase()}" data-index="${index}">
            </td>
            <td class="p-2 border">${index + 1}</td>
            <td class="p-2 border">${item[uraianField]}</td>
            <td class="p-2 border">${item.satuan}</td>
            <td class="p-2 border">Rp ${formatNumber(item[hargaField])}</td>
            <td class="p-2 border">${item.sumber || ""}</td>
            <td class="p-2 border">
                <input type="number" class="koefisien-input border rounded p-1 w-20" step="0.01" min="0.01" value="1">
            </td>
        `;

        tableBody.appendChild(row);
    });
};

// Helper function to format numbers with commas
function formatNumber(num) {
    return parseFloat(num).toLocaleString("id-ID", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
}

/**
 * Function to handle the detail edit action
 * Called when the edit button is clicked on a detail row
 */
window.handleDetailEdit = function (kategori, index) {
    // Close the context menu if it's open
    const contextMenu = document.getElementById("detail-context-menu");
    if (contextMenu) {
        contextMenu.classList.add("hidden");
    }

    // Get the detail item from the appropriate array
    let detailItem;
    if (kategori === "Bahan") {
        detailItem = window.detailBahan ? window.detailBahan[index] : null;
    } else if (kategori === "Upah") {
        detailItem = window.detailUpah ? window.detailUpah[index] : null;
    } else if (kategori === "Alat") {
        detailItem = window.detailAlat ? window.detailAlat[index] : null;
    }

    if (!detailItem) {
        window.showErrorToast("Item tidak ditemukan");
        return;
    }

    // Open the edit modal with the item details
    openEditItemModal(detailItem, kategori, index);
};

/**
 * Function to open the edit item modal with the details of the item to edit
 */
window.openEditItemModal = function (item, kategori, index) {
    // Set the modal title
    document.getElementById("item-modal-label").textContent = "Edit Item";

    // Store the original category for reference
    window.editingItemCategory = kategori;
    window.editingItemId = item.item_id;

    // Clear validation errors
    clearValidationErrors();

    // Store original values for change detection
    window.originalItemText = item.item_text || "";
    window.originalHargaDasar = parseFloat(item.harga_dasar).toFixed(2);

    // Set the hidden fields
    document.getElementById("editItemId").value = item.item_id || "";
    document.getElementById("editItemCategory").value = kategori;
    document.getElementById("editItemIndex").value = index;

    // Set the visible fields
    document.getElementById("editItemText").value = item.item_text || "";

    // Set satuan as label and hidden input
    document.getElementById("editItemSatuanLabel").innerText =
        item.satuan || "-";
    document.getElementById("editItemSatuan").value = item.satuan || "";

    // Set harga and koefisien
    document.getElementById("editItemHarga").value = parseFloat(
        item.harga_dasar
    ).toFixed(2);

    // Store original coefficient value for comparison later
    window.originalKoefisien = parseFloat(item.koefisien).toFixed(2);

    // Show the modal
    const modal = document.getElementById("editItemModal");
    if (modal) {
        modal.classList.remove("hidden");
    }
};

/**
 * Function to close the edit item modal
 */
window.closeEditItemModal = function () {
    const modal = document.getElementById("editItemModal");
    if (modal) {
        modal.classList.add("hidden");
    }
};

/**
 * Function to save the edited item
 */
window.saveItemEdit = function () {
    // Get the values from the form
    const itemId = document.getElementById("editItemId").value;
    const kategori = document.getElementById("editItemCategory").value;
    const index = parseInt(document.getElementById("editItemIndex").value);
    const itemText = document.getElementById("editItemText").value;
    const satuan = document.getElementById("editItemSatuan").value;
    const hargaDasar = parseFloat(
        document.getElementById("editItemHarga").value
    );

    // Check if there are any changes
    const isItemTextChanged = window.originalItemText !== itemText;
    const isHargaDasarChanged =
        window.originalHargaDasar !==
        document.getElementById("editItemHarga").value;

    if (!isItemTextChanged && !isHargaDasarChanged) {
        window.showWarningToast("Tidak ada perubahan data yang dilakukan");
        return;
    }

    // Validate inputs
    if (!itemText.trim()) {
        window.showWarningToast("Uraian tidak boleh kosong");
        return;
    }

    if (isNaN(hargaDasar) || hargaDasar <= 0) {
        window.showWarningToast("Harga dasar harus lebih dari 0");
        return;
    }

    // Calculate harga satuan using the existing koefisien
    const koefisien = detailItem
        ? detailItem.koefisien
        : window.originalKoefisien;
    const hargaSatuan = hargaDasar * koefisien;

    // Update the item in the appropriate array
    let detailItem;
    if (
        kategori === "Bahan" &&
        window.detailBahan &&
        window.detailBahan[index]
    ) {
        detailItem = window.detailBahan[index];
    } else if (
        kategori === "Upah" &&
        window.detailUpah &&
        window.detailUpah[index]
    ) {
        detailItem = window.detailUpah[index];
    } else if (
        kategori === "Alat" &&
        window.detailAlat &&
        window.detailAlat[index]
    ) {
        detailItem = window.detailAlat[index];
    }

    if (detailItem) {
        // Check if only the koefisien has changed
        // Per requirement: sumber changes to "Empiris" only if koefisien is changed
        // NOT when modifying hargaDasar or itemText
        // Since koefisien input is removed, we're not changing koefisien anymore
        const isKoefisienChanged = false;

        // Store previous values to determine what changed
        const oldItemText = detailItem.item_text;
        const oldHargaDasar = detailItem.harga_dasar;

        // Update the item properties
        detailItem.item_text = itemText;
        detailItem.harga_dasar = hargaDasar;
        // Keep the existing koefisien
        detailItem.harga_satuan = hargaSatuan;

        // Cek jika nama item/uraian berubah
        const isItemTextChanged = oldItemText !== itemText;

        // Set sumber to "Empiris" jika nama item/uraian berubah
        if (isItemTextChanged) {
            // Hanya ubah sumberAHS menjadi "Empiris" jika bukan admin
            if (typeof window.isAdmin !== "undefined" && !window.isAdmin) {
                const sumberInput = document.getElementById("sumberAHSP");
                if (sumberInput) {
                    sumberInput.value = "Empiris";
                    // Set the flag for customer changes
                    window.customerHasMadeChanges = true;
                }
                // Also update the detailItem's sumber property if it exists
                if (detailItem.hasOwnProperty("sumber")) {
                    detailItem.sumber = "Empiris";
                }
            } else {
                // Ini adalah admin, jangan ubah sumberAHS
                window.customerHasMadeChanges = true;
            }
        }

        // Update the table display
        if (typeof window.updateDetailTable === "function") {
            window.updateDetailTable(kategori);
        }

        // Close the modal
        closeEditItemModal();

        // Show success message
        window.showSuccessToast("Item berhasil diperbarui");

        // Save changes to the server
        saveChangesToServer(kategori, detailItem, index);
    } else {
        window.showErrorToast("Gagal memperbarui item. Item tidak ditemukan.");
    }
};

/**
 * Function to save the edited item to the server
 */
function saveChangesToServer(kategori, item, index) {
    // Get the CSRF token
    const token = document
        .querySelector('meta[name="csrf-token"]')
        ?.getAttribute("content");

    if (!token) {
        console.error("CSRF token not found");
        return;
    }

    // 1. First add a NEW master item in the database (upahs, alats, bahans)
    // Instead of updating, always create a new record
    let masterUrl;
    let masterData = {};

    // Build the data and URL based on category
    if (kategori === "Bahan") {
        masterUrl = "/bahan";
        masterData = {
            uraian_bahan: item.item_text,
            satuan: item.satuan,
            harga_bahan: item.harga_dasar,
            // Note: We're creating a new record, even for updates
            create_new: true, // Flag to indicate we want a new record
        };
    } else if (kategori === "Upah") {
        masterUrl = "/upah";
        masterData = {
            uraian_tenaga: item.item_text,
            satuan: item.satuan,
            harga: item.harga_dasar,
            create_new: true, // Flag to indicate we want a new record
        };
    } else if (kategori === "Alat") {
        masterUrl = "/alat";
        masterData = {
            uraian_alat: item.item_text,
            satuan: item.satuan,
            harga_alat: item.harga_dasar,
            create_new: true, // Flag to indicate we want a new record
        };
    } else {
        console.error("Invalid category");
        return;
    }

    // Always add a new master item to database, even for updates
    fetch(masterUrl, {
        method: "POST", // Always use POST to create a new record
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": token,
            Accept: "application/json",
        },
        body: JSON.stringify(masterData),
    })
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((result) => {
            // If successful, we have a new master item
            console.log("New master item created:", result);

            if (result.success && result.resource) {
                // Dapatkan ID baru dari item master yang baru dibuat
                const newItemId = result.resource.id;

                // Update item ID di memori untuk tampilan UI
                item.item_id = newItemId;

                console.log(
                    "Item master baru berhasil dibuat dengan ID:",
                    newItemId
                );
                console.log("Tidak melakukan update ke tabel ahsp_details");

                // Tidak ada request ke server untuk memperbarui ahsp_details
            }
        })
        // Tidak ada lagi permintaan ke server untuk ahsp_details, jadi tidak perlu menangani respons
        .catch((error) => {
            console.error("Error updating items:", error);
        });
}
