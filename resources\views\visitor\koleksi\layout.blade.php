@extends('layouts.visitor')

@section('title', 'Koleksi')

@section('styles')
<style>
    .collection-card {
        @apply bg-white dark:bg-dark-card rounded-lg shadow-md overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
    }
    .collection-card-header {
        @apply bg-gradient-to-r from-light-navbar via-[#0C7A4A] to-[#086643] dark:from-blue-900 dark:to-blue-950 text-white p-4;
    }
    .collection-card-body {
        @apply p-4;
    }
    .collection-card-footer {
        @apply bg-gray-50 dark:bg-dark-bg-secondary p-4 border-t border-gray-200 dark:border-gray-700;
    }
    .view-btn {
        @apply bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded;
    }
</style>
@endsection

@section('content')
    <!-- Header Section -->
    <section class="bg-gradient-to-r from-light-navbar via-[#0C7A4A] to-[#086643] dark:from-blue-900 dark:to-blue-950 text-white pt-32 pb-20 transition-colors duration-200">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl font-bold mb-6">@yield('collection-title', 'Koleksi')</h1>
            <p class="text-xl max-w-3xl mx-auto mb-4">@yield('collection-description', 'Lihat koleksi data yang tersedia di RAB Estimator.')</p>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-12 bg-light-bg dark:bg-dark-bg transition-colors duration-200">
        <div class="container mx-auto px-4">
            @yield('collection-content')
        </div>
    </section>
@endsection
