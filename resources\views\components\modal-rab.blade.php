<!-- resources/views/components/modal-rab.blade.php -->
<div id="rabKategoriModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 class="text-white dark:text-dark-text text-lg font-semibold">Data <PERSON><PERSON><PERSON></h2>
        <button type="button" onclick="closeKategoriModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-4 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto w-full" style="scrollbar-width: thin;">
      <div class="px-4 py-2 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div class="relative flex-grow">
          <input type="text" id="searchKategori" placeholder="Cari kategori..." class="w-full p-2 pl-8 border dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="fas fa-search text-gray-400"></i>
          </div>
        </div>
        <button type="button" onclick="openInputKategoriModal()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105 whitespace-nowrap">
          <i class="fas fa-plus-circle"></i> Tambah Kategori
        </button>
      </div>
      <div class="overflow-x-auto max-h-[400px] overflow-y-auto px-4">
        <!-- Tabel Daftar Kategori -->
        <input type="hidden" id="projectIdInput" value="{{ session('project_id') ?? request('project_id') }}">
        <table class="min-w-full bg-white dark:bg-dark-card border border-gray-200 dark:border-dark-border max-h-[300px]">
          <thead class="bg-blue-200 dark:bg-dark-accent/30 sticky top-0 z-10">
            <tr>
              <th class="px-4 py-2 border dark:border-dark-border dark:text-gray-200">No.</th>
              <th class="px-4 py-2 border dark:border-dark-border dark:text-gray-200">Kategori Pekerjaan</th>
              <th class="px-4 py-2 border dark:border-dark-border dark:text-gray-200">Aksi</th>
            </tr>
          </thead>
          <tbody id="kategoriTableBody">
            <!-- Baris kategori akan dimuat oleh JavaScript -->
          </tbody>
        </table>
      </div>
      <!-- Pagination Container for Kategori Modal -->
      <div id="kategoriPaginationControls" class="px-4 py-2">
        <!-- Pagination controls akan diisi oleh JavaScript -->
      </div>
      <div class="px-4 py-3 mt-4 border-t border-gray-200 dark:border-dark-border flex justify-center">
        <button type="button" onclick="closeKategoriModal()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-check-circle mr-1"></i> Selesai</button>
      </div>
    </div>
  </div>
</div>

<!-- Context Menu for Item Actions -->
<div id="item-context-menu" class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-35 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
    <button onclick="openEditJudulModal()" class="w-full px-4 py-2 text-left text-blue-500 hover:bg-blue-100 hover:text-blue-700 text-sm flex items-center">
        <i class="fas fa-edit mr-2"></i> Edit Judul
    </button>
    <button onclick="openVolumeModal(window.currentItem.satuan, window.currentItem.itemId, window.currentItem.hargaSatuan)" class="w-full px-4 py-2 text-left text-blue-500 hover:bg-blue-100 hover:text-blue-700 text-sm flex items-center">
        <i class="fas fa-edit mr-2"></i> Edit Volume
    </button>
    <button onclick="editAhsp(window.currentItem.itemId, window.currentItem.kategoriId)" class="w-full px-4 py-2 text-left text-blue-500 hover:bg-blue-100 hover:text-blue-700 text-sm flex items-center">
        <i class="fas fa-edit mr-2"></i> Edit Ahsp
    </button>
    <button onclick="deleteItem(window.currentItem.itemId)" class="w-full px-4 py-2 text-left hover:bg-blue-100 text-sm flex items-center hover:text-red-700 text-red-500">
        <i class="fas fa-trash-alt mr-2"></i> Hapus
    </button>
</div>

<!-- Context Menu for Kategori Actions -->
<div id="kategori-context-menu" class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-35 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
    <button onclick="editKategori()" class="w-full px-4 py-2 text-left text-blue-500 hover:bg-blue-100 hover:text-blue-700 text-sm flex items-center">
        <i class="fas fa-edit mr-2"></i> Edit
    </button>
    <button onclick="deleteKategoriContext()" class="w-full px-4 py-2 text-left hover:bg-blue-100 text-sm flex items-center hover:text-red-700 text-red-500">
        <i class="fas fa-trash-alt mr-2"></i> Hapus
    </button>
</div>

<!-- Modal Input Kategori Baru / Edit -->
<div id="inputKategoriModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-md relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 id="inputKategoriModalTitle" class="text-white dark:text-dark-text text-lg font-semibold">Tambah Kategori</h2>
        <button type="button" onclick="closeInputKategoriModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
      <div class="mb-4">
      <label for="newKategoriInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Nama Kategori</label>
      <input type="text" id="newKategoriInput" class="mt-1 block w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent" placeholder="Masukkan kategori" required>
    </div>
      
      <div class="border-t dark:border-dark-border pt-4 flex justify-end space-x-3">
        <button type="button" onclick="closeInputKategoriModal()" class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow">
        <i class="fas fa-times-circle mr-1"></i> Batal
      </button>
      <!-- Tombol untuk mode tambah -->
        <button id="btnTambahKategori" type="button" onclick="saveNewKategori()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
        <i class="fas fa-save mr-1"></i> Simpan
      </button>
      <!-- Tombol untuk mode edit, disembunyikan secara default -->
        <button id="btnSimpanPerubahanKategori" type="button" onclick="updateKategori()" class="hidden bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
        <i class="fas fa-save mr-1"></i> Simpan
      </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Input Item Pekerjaan -->
<div id="inputItemModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 class="text-white dark:text-dark-text text-lg font-semibold">Daftar Analisa Harga Satuan Pekerjaan</h2>
        <button type="button" onclick="closeItemModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
      <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
      <!-- Input pencarian -->
      <div class="mb-4 flex items-center gap-2">
        <div class="relative flex-grow">
          <input type="text" id="searchAhsp" placeholder="Cari data..." class="p-2 pl-8 border rounded w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200" onkeyup="filterAhspTable()">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="fas fa-search text-gray-400"></i>
          </div>
        </div>
        <button type="button" onclick="openFullFormModal(false, '', null, '', '', '', true)" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105 whitespace-nowrap">
          <i class="fas fa-plus-circle"></i> Tambah Analisa
        </button>
      </div>
      
      <!-- Tabel List Data AHS -->
      <div class="overflow-x-auto max-h-[400px] overflow-y-auto">
        <table id="ahspTable" class="text-sm w-full text-left border-collapse">
          <thead class="bg-blue-200 dark:bg-dark-accent/30 sticky top-0 z-10">
            <tr>
              <th class="p-2 border text-center">Pilih</th>
              <th class="p-2 border">No.</th>
              <th class="p-2 border">Judul</th>
              <th class="p-2 border">Satuan</th>
              <th class="p-2 border">Harga Satuan</th>
              <th class="p-2 border">Sumber</th>
            </tr>
          </thead>
          <tbody id="ahspTableBody">
            <!-- Data akan diisi oleh JavaScript -->
          </tbody>
        </table>
      </div>
      
      <!-- Pagination Container -->
      <div id="ahspPagination" class="px-4 py-2">
        <!-- Pagination controls akan diisi oleh JavaScript -->
      </div>
      
      <div class="mt-6 flex justify-end space-x-3">
        <button type="button" onclick="closeItemModal()" class="bg-gray-500 hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow">
          <i class="fas fa-times-circle"></i> Batal
        </button>
        <button type="button" onclick="saveSelectedItems()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-plus-circle"></i> Tambah
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Edit Judul Item -->
<div id="editJudulModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
  <div class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-md relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent overflow-hidden">
    <!-- Header -->
    <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
      <div class="flex justify-between items-center">
        <h2 class="text-white dark:text-dark-text text-lg font-semibold">Edit Uraian Pekerjaan</h2>
        <button type="button" onclick="closeEditJudulModal()" 
            class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
          <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Content -->
    <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto" style="scrollbar-width: thin;">
      <input type="hidden" id="editItemId">
      <div class="mb-4">
        <label for="editUraianInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Uraian Pekerjaan</label>
        <textarea id="editUraianInput" class="mt-1 block w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent" rows="4" placeholder="Masukkan uraian pekerjaan" required></textarea>
      </div>
      
      <div class="border-t dark:border-dark-border pt-4 flex justify-end space-x-3">
        <button type="button" onclick="closeEditJudulModal()" class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow">
          <i class="fas fa-times-circle mr-1"></i> Batal
        </button>
        <button type="button" onclick="saveEditedJudul()" class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
          <i class="fas fa-save mr-1"></i> Simpan
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Script untuk fitur pencarian kategori -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Menambahkan event listener untuk input pencarian kategori
    const searchKategoriInput = document.getElementById('searchKategori');
    if (searchKategoriInput) {
      searchKategoriInput.addEventListener('keyup', filterKategoriTable);
    }
  });

  // Fungsi untuk memfilter tabel kategori berdasarkan input pencarian
  function filterKategoriTable() {
    const searchInput = document.getElementById('searchKategori');
    const filter = searchInput.value.toUpperCase();
    const tableBody = document.getElementById('kategoriTableBody');
    const rows = tableBody.getElementsByTagName('tr');

    // Loop melalui semua baris tabel dan sembunyikan yang tidak cocok dengan pencarian
    for (let i = 0; i < rows.length; i++) {
      const kategoriCell = rows[i].getElementsByTagName('td')[1]; // Kolom kategori pekerjaan
      if (kategoriCell) {
        const textValue = kategoriCell.textContent || kategoriCell.innerText;
        if (textValue.toUpperCase().indexOf(filter) > -1) {
          rows[i].style.display = '';
        } else {
          rows[i].style.display = 'none';
        }
      }
    }
  }
</script>
