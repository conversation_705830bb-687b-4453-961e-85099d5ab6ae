document.addEventListener("DOMContentLoaded", function () {
    // Cek apakah ada alert yang tersimpan terlebih dahulu sebelum pengecekan flag
    const savedAlert = localStorage.getItem("ahspAlert");
    const savedAlertTime = localStorage.getItem("ahspAlertTime");
    const successAlert = localStorage.getItem("ahspSuccessAlert");
    const successAlertTime = localStorage.getItem("ahspSuccessTime");

    // Proses alert success jika ada (prioritas lebih tinggi)
    if (successAlert && successAlertTime) {
        const currentTime = new Date().getTime();
        const alertTime = parseInt(successAlertTime);

        // Jika alert success disimpan kurang dari 1 menit yang lalu
        if (currentTime - alertTime < 60000) {
            setTimeout(() => {
                window.showSuccessToast(successAlert, null, null, 3000);
            }, 1000); // Delay lebih lama agar muncul setelah halaman benar-benar siap
        }

        // Hapus alert success dari localStorage setelah ditampilkan
        localStorage.removeItem("ahspSuccessAlert");
        localStorage.removeItem("ahspSuccessTime");

        // Hapus juga alert error jika ada, karena success lebih diprioritaskan
        localStorage.removeItem("ahspAlert");
        localStorage.removeItem("ahspAlertTime");
    }
    // Proses alert error hanya jika tidak ada alert success
    else if (savedAlert && savedAlertTime) {
        const currentTime = new Date().getTime();
        const alertTime = parseInt(savedAlertTime);

        // Jika alert disimpan kurang dari 1 menit yang lalu
        if (currentTime - alertTime < 60000) {
            setTimeout(() => {
                window.showInfoToast(savedAlert, null, null, 3000);
            }, 500); // Delay sedikit agar halaman selesai loading
        }

        // Hapus alert dari localStorage setelah ditampilkan
        localStorage.removeItem("ahspAlert");
        localStorage.removeItem("ahspAlertTime");
    }

    // Cek apakah sudah ada flag bahwa event listener sudah dipasang
    if (window.ahspInitialized) return;
    window.ahspInitialized = true;
    // --- Variabel Global ---
    let currentRecord = null;
    window.currentAhsId = null;
    window.detailBahan = [];
    window.detailUpah = [];
    window.detailAlat = [];
    window.currentDetailCategory = null; // Untuk modal detail table

    // Pagination for Detail Modal
    window.currentDetailModalData = []; // Holds the full list for the current category (Bahan, Upah, Alat)
    window.detailModalPagination = {
        currentPage: 1,
        itemsPerPage: 10,
        totalPages: 1,
        filteredData: [], // Holds currently filtered/searched data within the full list
    };

    // === Event Listener untuk Tombol Action (Context Menu) di halaman utama ===
    document.querySelectorAll(".action-btn").forEach((button) => {
        button.addEventListener("click", (e) => {
            e.preventDefault();
            currentRecord = {
                id: e.currentTarget.dataset.id,
                kode: e.currentTarget.dataset.kode,
                judul: e.currentTarget.dataset.judul,
                satuan: e.currentTarget.dataset.satuan,
                x: e.pageX,
                y: e.pageY,
            };
            positionContextMenu(e);
            showContextMenu();
        });
    });

    function positionContextMenu(e) {
        const contextMenu = document.getElementById("context-menu");
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const menuWidth = 128;
        const menuHeight = 80;
        let x = e.pageX;
        if (x + menuWidth > viewportWidth) {
            x = viewportWidth - menuWidth;
        }
        let y = e.pageY;
        if (y + menuHeight > viewportHeight) {
            y = viewportHeight - menuHeight;
        }
        contextMenu.style.left = `${x}px`;
        contextMenu.style.top = `${y}px`;
    }

    function showContextMenu() {
        const contextMenu = document.getElementById("context-menu");
        contextMenu.classList.remove("hidden");
    }

    document.addEventListener("click", (e) => {
        const contextMenu = document.getElementById("context-menu");
        if (
            contextMenu &&
            !contextMenu.contains(e.target) &&
            !e.target.closest(".action-btn")
        ) {
            contextMenu.classList.add("hidden");
        }
    });

    window.handleEdit = function () {
        if (currentRecord) {
            openFullFormModal(
                true,
                currentRecord.judul,
                currentRecord.id,
                currentRecord.kode,
                currentRecord.satuan
            );
            document.getElementById("btnUpdate").classList.add("hidden"); // Hide the new button
        }
        document.getElementById("context-menu").classList.add("hidden");
    };

    // Fungsi showCustomConfirm telah dipindahkan ke file confirm.js
    // dan tersedia secara global sebagai window.showCustomConfirm

    window.handleDelete = function () {
        if (currentRecord) {
            showCustomConfirm(
                `Anda yakin ingin menghapus "${currentRecord.judul}"?`,
                () => {
                    const form = document.createElement("form");
                    form.action = `/ahs/${currentRecord.id}`;
                    form.method = "POST";
                    form.innerHTML = `
                        <input type="hidden" name="_token" value="${window.csrfToken}">
                        <input type="hidden" name="_method" value="DELETE">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                },
                () => {
                    console.log("Hapus dibatalkan.");
                }
            );
        }
        document.getElementById("context-menu").classList.add("hidden");
    };

    // === Fungsi Dasar Modal ===
    window.openModal = function (id) {
        const modal = document.getElementById(id);
        if (modal) modal.classList.remove("hidden");
    };

    window.closeModal = function (id) {
        const modal = document.getElementById(id);
        if (modal) modal.classList.add("hidden");
    };

    // === Utility Fungsi ===
    function setInputValue(id, value) {
        const input = document.getElementById(id);
        if (input)
            input.value = value !== undefined && value !== null ? value : "";
    }

    function setMethodInput(form, method) {
        let methodInput = form.querySelector('input[name="_method"]');
        if (!methodInput) {
            methodInput = document.createElement("input");
            methodInput.type = "hidden";
            methodInput.name = "_method";
            methodInput.value = method;
            form.appendChild(methodInput);
        } else {
            methodInput.value = method;
        }
    }

    function removeMethodInput(form) {
        let methodInput = form.querySelector('input[name="_method"]');
        if (methodInput) methodInput.remove();
    }

    // === Modal Full Form AHSP ===
    window.openFullFormModal = function (
        isEdit,
        judul = "",
        id = null,
        kode = "",
        satuan = "",
        sumber = "",
        openedFromInputItemModal = false
    ) {
        const modalId = "fullFormModal";
        const form = document.getElementById("ahspForm");
        const titleEl = document.getElementById("formTitle");
        if (!form || !titleEl) return;

        // Add a flag to track if customer has made changes
        window.customerHasMadeChanges = false;

        if (isEdit && id) {
            window.currentAhsId = id;
            titleEl.innerText = "Edit Data AHSP";
            form.action = window.ahsBaseUrl + "/" + id + "/detail";
            let methodInput = form.querySelector('input[name="_method"]');
            if (!methodInput) {
                setMethodInput(form, "PUT");
            } else {
                methodInput.value = "PUT";
            }
            setInputValue("judulAHSP", judul);
            setInputValue("kodeAHSP", kode);
            setInputValue("satuanAHSP", satuan);

            // Handle based on user role
            if (!window.isAdmin) {
                // For regular customers, initialize as empty but make read-only
                // Don't set to "Empiris" by default, only when changes are made
                document.getElementById("sumberAHSP").readOnly = true;
            } else {
                // For admin, use empty value and allow editing
                setInputValue("sumberAHSP", "");
                document.getElementById("sumberAHSP").readOnly = false;
            }

            document.getElementById("btnTambah").classList.add("hidden");
            document.getElementById("btnUpdate").classList.remove("hidden");
            document.getElementById("btnUpdate1").classList.remove("hidden"); // Show the new button
            document.getElementById("satuanAHSP").classList.add("hidden");
            document
                .getElementById("satuanAHSPInput")
                .classList.remove("hidden");
            document.getElementById("satuanAHSPInput").readOnly = true;
            setInputValue("satuanAHSPInput", satuan);

            fetch(window.ahsBaseUrl + "/" + id + "/detail")
                .then((response) => {
                    if (!response.ok) {
                        return response.text().then((text) => {
                            throw new Error(text);
                        });
                    }
                    return response.json();
                })
                .then((data) => {
                    setInputValue(
                        "overheadInput",
                        data.overhead !== undefined ? data.overhead : ""
                    );
                    setInputValue("kodeAHSP", data.kode || kode);
                    setInputValue("satuanAHSP", data.satuan || satuan);

                    // Simpan informasi creator_role untuk digunakan saat update
                    window.originalCreatorRole = data.creator_role || "";
                    console.log("Creator role:", window.originalCreatorRole);

                    // For customer, initially show original source
                    if (!window.isAdmin) {
                        setInputValue("sumberAHSP", data.sumber || sumber);
                    } else {
                        setInputValue("sumberAHSP", data.sumber || sumber);
                    }

                    window.detailBahan = data.bahan || [];
                    window.detailUpah = data.upah || [];
                    window.detailAlat = data.alat || [];
                    updateDetailTable("Bahan");
                    updateDetailTable("Upah");
                    updateDetailTable("Alat");
                })
                .catch((error) => {
                    console.error("Error fetching details:", error);
                    showCustomAlert(
                        "Gagal memuat detail data. Periksa console untuk detail."
                    );
                });
        } else {
            window.currentAhsId = null;
            titleEl.innerText = "Input Data AHSP";
            form.action = window.ahsBaseUrl;
            let methodInput = form.querySelector('input[name="_method"]');
            if (methodInput) removeMethodInput(form);
            form.reset();
            window.clearAllDetailTables();
            setInputValue("kodeAHSP", "");
            setInputValue("satuanAHSP", "");

            // Handle based on user role
            if (!window.isAdmin) {
                // For regular customers, initialize as empty but make read-only
                // Don't set to "Empiris" by default, only when changes are made
                document.getElementById("sumberAHSP").readOnly = true;
            } else {
                // For admin, use empty value and allow editing
                setInputValue("sumberAHSP", "");
                document.getElementById("sumberAHSP").readOnly = false;
            }

            document.getElementById("btnTambah").classList.remove("hidden");
            document.getElementById("btnUpdate").classList.add("hidden");
            document.getElementById("btnUpdate1").classList.add("hidden"); // Hide the new button
            document.getElementById("satuanAHSP").classList.remove("hidden");
            document.getElementById("satuanAHSPInput").classList.add("hidden");
            document.getElementById("satuanAHSPInput").readOnly = false;
        }

        // Atur visibilitas tombol Tambah, Tambah1, dan Update
        const btnTambahStd = document.getElementById("btnTambah");
        const btnTambah1 = document.getElementById("btnTambah1");
        const btnUpdateStd = document.getElementById("btnUpdate");
        const btnUpdate1 = document.getElementById("btnUpdate1"); // Tombol Update yg lain jika ada

        if (isEdit) {
            btnTambahStd.classList.add("hidden");
            btnTambah1.classList.add("hidden");
            btnUpdateStd.classList.remove("hidden"); // Tampilkan btnUpdate standar
            // Atur btnUpdate1 jika ada logika khusus saat edit
            // btnUpdate1.classList.remove("hidden"); // Contoh jika btnUpdate1 juga dipakai saat edit
            if (btnUpdate1) btnUpdate1.classList.remove("hidden"); // Tampilkan jika ada
        } else {
            // Mode Tambah baru
            btnUpdateStd.classList.add("hidden");
            if (btnUpdate1) btnUpdate1.classList.add("hidden");

            if (openedFromInputItemModal) {
                btnTambahStd.classList.add("hidden");
                btnTambah1.classList.remove("hidden");
            } else {
                btnTambahStd.classList.remove("hidden");
                btnTambah1.classList.add("hidden");
            }
        }

        openModal(modalId);
    };

    window.closeFullFormModal = function () {
        closeModal("fullFormModal");
    };

    // === Tombol "Tambah" (Simpan Data via AJAX dengan reload) ===
    const btnTambahElement = document.getElementById("btnTambah");
    if (btnTambahElement) {
        btnTambahElement.addEventListener("click", function () {
            // ✅ CEK SUBSCRIPTION UNTUK AHSP EMPIRIS
            const sumberElement = document.getElementById("sumberAHSP");
            const sumber = sumberElement ? sumberElement.value.trim() : "";

            console.log('AHSP btnTambah - Sumber:', sumber);

            if (sumber === "Empiris") {
                console.log('AHSP btnTambah - Checking subscription for Empiris source');
                // Cek apakah user memiliki akses ke fitur AHSP Empiris
                if (typeof window.subscriptionChecker !== 'undefined' &&
                    typeof window.subscriptionChecker.hasFeatureAccess === 'function' &&
                    !window.subscriptionChecker.hasFeatureAccess('AHSP Empiris')) {

                    if (typeof window.subscriptionChecker.showFeatureRestrictedToast === 'function') {
                        window.subscriptionChecker.showFeatureRestrictedToast('AHSP Empiris');
                    } else if (typeof window.showInfoToast === 'function') {
                        window.showInfoToast(
                            'Fitur "AHSP Empiris" tidak tersedia dalam paket langganan Anda. Silakan upgrade paket untuk mengakses fitur ini.',
                            'Fitur Terbatas',
                            null,
                            7000,
                            '/subscriptions'
                        );
                    }
                    return;
                }
            } else {
                console.log('AHSP btnTambah - Non-Empiris source, proceeding normally');
            }

            const btnInner = this; // Merujuk ke tombol btnTambah
            btnInner.innerHTML =
                '<i class="fas fa-spinner fa-spin spinner mr-2"></i> <span>Memproses...</span>';
            btnInner.disabled = true;
            btnInner.classList.add("loading-btn");

            if (computeGrandTotal() === 0) {
                // Simpan pesan di localStorage agar tetap ada setelah reload
                localStorage.setItem(
                    "ahspAlert",
                    "Harga satuan tidak boleh '0'"
                );
                localStorage.setItem("ahspAlertTime", new Date().getTime());
                window.showWarningToast("Harga satuan tidak boleh '0'");
                btnInner.innerHTML =
                    '<i class="fas fa-plus-circle"></i> Tambah';
                btnInner.disabled = false;
                btnInner.classList.remove("loading-btn");
                return;
            }

            const form = document.getElementById("ahspForm");
            if (!form.checkValidity()) {
                form.reportValidity();
                btnInner.innerHTML =
                    '<i class="fas fa-plus-circle"></i> Tambah';
                btnInner.disabled = false;
                btnInner.classList.remove("loading-btn");
                return;
            }

            const allDetails = {
                bahan: window.detailBahan,
                upah: window.detailUpah,
                alat: window.detailAlat,
            };
            const judul = document.getElementById("judulAHSP").value.trim();
            const kode = document.getElementById("kodeAHSP").value.trim();
            const overhead = document
                .getElementById("overheadInput")
                .value.trim();
            const satuanElement = document.getElementById("satuanAHSP");
            const satuanInputElement =
                document.getElementById("satuanAHSPInput");
            let satuan =
                satuanElement &&
                window.getComputedStyle(satuanElement).display !== "none"
                    ? satuanElement.value.trim()
                    : satuanInputElement &&
                      window.getComputedStyle(satuanInputElement).display !==
                          "none"
                    ? satuanInputElement.value.trim()
                    : "";
            // sumber sudah dideklarasikan di atas untuk pengecekan subscription
            const data = {
                judul,
                satuan,
                kode,
                overhead,
                sumber,
                detail: allDetails,
            };

            fetch(window.ahsDetailStoreRoute, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": window.csrfToken,
                },
                body: JSON.stringify(data),
            })
                .then((response) =>
                    response.ok
                        ? response.json()
                        : response.text().then((text) => {
                              throw new Error(text);
                          })
                )
                .then((result) => {
                    if (result.success) {
                        localStorage.setItem(
                            "ahspSuccessAlert",
                            result.message ||
                                "Data AHSP berhasil disimpan. Halaman akan dimuat ulang."
                        );
                        localStorage.setItem(
                            "ahspSuccessTime",
                            new Date().getTime()
                        );
                        btnInner.innerHTML =
                            '<i class="fas fa-plus-circle"></i> Tambah';
                        btnInner.disabled = false;
                        btnInner.classList.remove("loading-btn");
                        window.location.reload(); // INI YANG PENTING UNTUK btnTambah
                    } else {
                        btnInner.innerHTML =
                            '<i class="fas fa-plus-circle"></i> Tambah';
                        btnInner.disabled = false;
                        btnInner.classList.remove("loading-btn");
                        window.showErrorToast(
                            result.message ||
                                "Terjadi kesalahan saat menyimpan data detail."
                        );
                    }
                })
                .catch((error) => {
                    btnInner.innerHTML =
                        '<i class="fas fa-plus-circle"></i> Tambah';
                    btnInner.disabled = false;
                    btnInner.classList.remove("loading-btn");

                    // Check if it's a subscription error
                    if (error.response && error.response.status === 403 && error.response.data && error.response.data.show_toast) {
                        if (typeof window.showSubscriptionErrorToast === 'function') {
                            window.showSubscriptionErrorToast(error.response.data);
                        } else if (typeof window.showInfoToast === 'function') {
                            window.showInfoToast(
                                error.response.data.message || 'Fitur ini tidak tersedia dalam paket langganan Anda.',
                                'Fitur Terbatas',
                                null,
                                7000,
                                error.response.data.upgrade_url || '/subscriptions'
                            );
                        }
                    } else {
                        window.showErrorToast(
                            "Terjadi kesalahan. Periksa console untuk detail."
                        );
                        console.error("Error:", error);
                    }
                });
        });
    }

    // === Tombol "Tambah1" (Simpan Data via AJAX tanpa reload, untuk inputItemModal) ===
    const btnTambah1Element = document.getElementById("btnTambah1");
    if (btnTambah1Element) {
        btnTambah1Element.addEventListener("click", function () {
            // ✅ CEK SUBSCRIPTION UNTUK AHSP EMPIRIS
            const sumberElement = document.getElementById("sumberAHSP");
            const sumber = sumberElement ? sumberElement.value.trim() : "";

            if (sumber === "Empiris") {
                // Cek apakah user memiliki akses ke fitur AHSP Empiris
                if (typeof window.subscriptionChecker !== 'undefined' &&
                    typeof window.subscriptionChecker.hasFeatureAccess === 'function' &&
                    !window.subscriptionChecker.hasFeatureAccess('AHSP Empiris')) {

                    if (typeof window.subscriptionChecker.showFeatureRestrictedToast === 'function') {
                        window.subscriptionChecker.showFeatureRestrictedToast('AHSP Empiris');
                    } else if (typeof window.showInfoToast === 'function') {
                        window.showInfoToast(
                            'Fitur "AHSP Empiris" tidak tersedia dalam paket langganan Anda. Silakan upgrade paket untuk mengakses fitur ini.',
                            'Fitur Terbatas',
                            null,
                            7000,
                            '/subscriptions'
                        );
                    }
                    return;
                }
            }

            const btnInner = this; // Merujuk ke tombol btnTambah1
            btnInner.innerHTML =
                '<i class="fas fa-spinner fa-spin spinner mr-2"></i> <span>Memproses...</span>';
            btnInner.disabled = true;
            btnInner.classList.add("loading-btn");

            if (computeGrandTotal() === 0) {
                window.showWarningToast("Harga satuan tidak boleh '0'");
                btnInner.innerHTML =
                    '<i class="fas fa-plus-circle"></i> Tambah';
                btnInner.disabled = false;
                btnInner.classList.remove("loading-btn");
                return;
            }

            const form = document.getElementById("ahspForm");
            if (!form.checkValidity()) {
                form.reportValidity();
                btnInner.innerHTML =
                    '<i class="fas fa-plus-circle"></i> Tambah';
                btnInner.disabled = false;
                btnInner.classList.remove("loading-btn");
                return;
            }

            const allDetails = {
                bahan: window.detailBahan,
                upah: window.detailUpah,
                alat: window.detailAlat,
            };
            const judul = document.getElementById("judulAHSP").value.trim();
            const kode = document.getElementById("kodeAHSP").value.trim();
            const overhead = document
                .getElementById("overheadInput")
                .value.trim();
            const satuanElement = document.getElementById("satuanAHSP");
            const satuanInputElement =
                document.getElementById("satuanAHSPInput");
            let satuan =
                satuanElement &&
                window.getComputedStyle(satuanElement).display !== "none"
                    ? satuanElement.value.trim()
                    : satuanInputElement &&
                      window.getComputedStyle(satuanInputElement).display !==
                          "none"
                    ? satuanInputElement.value.trim()
                    : "";
            // sumber sudah dideklarasikan di atas untuk pengecekan subscription
            const data = {
                judul,
                satuan,
                kode,
                overhead,
                sumber,
                detail: allDetails,
            };

            fetch(window.ahsDetailStoreRoute, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": window.csrfToken,
                },
                body: JSON.stringify(data),
            })
                .then((response) =>
                    response.ok
                        ? response.json()
                        : response.text().then((text) => {
                              throw new Error(text);
                          })
                )
                .then((result) => {
                    if (result.success) {
                        window.showSuccessToast(
                            result.message || "Data AHSP berhasil disimpan."
                        );
                        btnInner.innerHTML =
                            '<i class="fas fa-plus-circle"></i> Tambah';
                        btnInner.disabled = false;
                        btnInner.classList.remove("loading-btn");
                        closeFullFormModal(); // Tutup modal saat ini
                        if (typeof window.refreshAHSPData === "function") {
                            // Untuk inputItemModal di rab.js
                            window.refreshAHSPData();
                        }
                    } else {
                        btnInner.innerHTML =
                            '<i class="fas fa-plus-circle"></i> Tambah';
                        btnInner.disabled = false;
                        btnInner.classList.remove("loading-btn");
                        window.showErrorToast(
                            result.message ||
                                "Terjadi kesalahan saat menyimpan data."
                        );
                    }
                })
                .catch((error) => {
                    btnInner.innerHTML =
                        '<i class="fas fa-plus-circle"></i> Tambah';
                    btnInner.disabled = false;
                    btnInner.classList.remove("loading-btn");

                    // Check if it's a subscription error
                    if (error.response && error.response.status === 403 && error.response.data && error.response.data.show_toast) {
                        if (typeof window.showSubscriptionErrorToast === 'function') {
                            window.showSubscriptionErrorToast(error.response.data);
                        } else if (typeof window.showInfoToast === 'function') {
                            window.showInfoToast(
                                error.response.data.message || 'Fitur ini tidak tersedia dalam paket langganan Anda.',
                                'Fitur Terbatas',
                                null,
                                7000,
                                error.response.data.upgrade_url || '/subscriptions'
                            );
                        }
                    } else {
                        window.showErrorToast(
                            "Terjadi kesalahan. Periksa console untuk detail."
                        );
                        console.error("Error:", error);
                    }
                });
        });
    }

    // === Tombol "Update" (Simpan Perubahan Data via AJAX dengan reload) ===
    const btnUpdateElement = document.getElementById("btnUpdate");
    if (btnUpdateElement) {
        btnUpdateElement.addEventListener("click", function () {
            // ✅ CEK SUBSCRIPTION UNTUK AHSP EMPIRIS
            const sumberElement = document.getElementById("sumberAHSP");
            const sumber = sumberElement ? sumberElement.value.trim() : "";

            if (sumber === "Empiris") {
                // Cek apakah user memiliki akses ke fitur AHSP Empiris
                if (typeof window.subscriptionChecker !== 'undefined' &&
                    typeof window.subscriptionChecker.hasFeatureAccess === 'function' &&
                    !window.subscriptionChecker.hasFeatureAccess('AHSP Empiris')) {

                    if (typeof window.subscriptionChecker.showFeatureRestrictedToast === 'function') {
                        window.subscriptionChecker.showFeatureRestrictedToast('AHSP Empiris');
                    } else if (typeof window.showInfoToast === 'function') {
                        window.showInfoToast(
                            'Fitur "AHSP Empiris" tidak tersedia dalam paket langganan Anda. Silakan upgrade paket untuk mengakses fitur ini.',
                            'Fitur Terbatas',
                            null,
                            7000,
                            '/subscriptions'
                        );
                    }
                    return;
                }
            }

            handleAhspUpdate(this, true); // true untuk reload halaman setelah sukses
        });
    }

    // === Tombol "Update1" (Simpan Perubahan Data via AJAX) ===
    // Perilaku Update1 bisa disesuaikan jika berbeda, untuk sekarang sama dengan btnUpdate (dengan reload)
    // atau bisa dibuat tanpa reload jika itu tujuannya.
    // Untuk saat ini, kita buat perilakunya sama dengan btnUpdate (dengan reload)
    // Jika btnUpdate1 dimaksudkan untuk tidak reload (misalnya, mirip btnTambah1), parameter kedua bisa false
    const btnUpdate1Element = document.getElementById("btnUpdate1");
    if (btnUpdate1Element) {
        btnUpdate1Element.addEventListener("click", function () {
            // ✅ CEK SUBSCRIPTION UNTUK AHSP EMPIRIS
            const sumberElement = document.getElementById("sumberAHSP");
            const sumber = sumberElement ? sumberElement.value.trim() : "";

            if (sumber === "Empiris") {
                // Cek apakah user memiliki akses ke fitur AHSP Empiris
                if (typeof window.subscriptionChecker !== 'undefined' &&
                    typeof window.subscriptionChecker.hasFeatureAccess === 'function' &&
                    !window.subscriptionChecker.hasFeatureAccess('AHSP Empiris')) {

                    if (typeof window.subscriptionChecker.showFeatureRestrictedToast === 'function') {
                        window.subscriptionChecker.showFeatureRestrictedToast('AHSP Empiris');
                    } else if (typeof window.showInfoToast === 'function') {
                        window.showInfoToast(
                            'Fitur "AHSP Empiris" tidak tersedia dalam paket langganan Anda. Silakan upgrade paket untuk mengakses fitur ini.',
                            'Fitur Terbatas',
                            null,
                            7000,
                            '/subscriptions'
                        );
                    }
                    return;
                }
            }

            handleAhspUpdate(this, true); // true untuk reload halaman setelah sukses
        });
    }

    // Fungsi untuk menangani Update Data AHSP
    function handleAhspUpdate(buttonElement, shouldReload) {
        const btnInner = buttonElement;
        btnInner.innerHTML =
            '<i class="fas fa-spinner fa-spin spinner mr-2"></i> <span>Memproses...</span>';
        btnInner.disabled = true;
        btnInner.classList.add("loading-btn");

        if (computeGrandTotal() === 0) {
            localStorage.setItem("ahspAlert", "Harga satuan tidak boleh '0'");
            localStorage.setItem("ahspAlertTime", new Date().getTime());
            window.showWarningToast("Harga satuan tidak boleh '0'");
            btnInner.innerHTML = '<i class="fas fa-save"></i> Simpan Perubahan';
            btnInner.disabled = false;
            btnInner.classList.remove("loading-btn");
            return;
        }

        const form = document.getElementById("ahspForm");
        if (!form.checkValidity()) {
            form.reportValidity();
            btnInner.innerHTML = '<i class="fas fa-save"></i> Simpan Perubahan';
            btnInner.disabled = false;
            btnInner.classList.remove("loading-btn");
            return;
        }

        const allDetails = {
            bahan: window.detailBahan,
            upah: window.detailUpah,
            alat: window.detailAlat,
        };
        const judul = document.getElementById("judulAHSP").value.trim();
        const kode = document.getElementById("kodeAHSP").value.trim();
        const overhead = document.getElementById("overheadInput").value.trim();
        const satuanElement = document.getElementById("satuanAHSP");
        const satuanInputElement = document.getElementById("satuanAHSPInput");
        let satuan =
            satuanElement &&
            window.getComputedStyle(satuanElement).display !== "none"
                ? satuanElement.value.trim()
                : satuanInputElement &&
                  window.getComputedStyle(satuanInputElement).display !== "none"
                ? satuanInputElement.value.trim()
                : "";

        // Ambil nilai sumber dari input, pastikan tidak null jika elemen tidak ditemukan
        const sumberElement = document.getElementById("sumberAHSP");
        const sumber = sumberElement ? sumberElement.value.trim() : "";

        // Tambahkan flag create_new_ahs hanya jika pembuat asli adalah admin dan user saat ini adalah customer
        // Untuk mendapatkan informasi creator_role, kita perlu memeriksa window.originalCreatorRole
        const data = {
            judul,
            satuan,
            kode,
            overhead,
            sumber, // Tambahkan sumber ke data
            detail: allDetails,
            _method: "PUT", // Eksplisit untuk FormData atau JSON body jika server mengharapkannya
            create_new_ahs:
                !window.isAdmin && window.originalCreatorRole === "admin", // Hanya true jika customer mengedit data admin
        };

        // URL untuk update sudah di set di form.action saat openFullFormModal (isEdit = true)
        // form.action akan menjadi /ahs/{id}/detail
        fetch(form.action, {
            method: "POST", // Method tetap POST karena PUT disimulasikan dengan _method
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": window.csrfToken,
                "X-HTTP-Method-Override": "PUT", // Header alternatif jika server mendukung
            },
            body: JSON.stringify(data),
        })
            .then((response) =>
                response.ok
                    ? response.json()
                    : response.text().then((text) => {
                          throw new Error(text || "Server error occurred");
                      })
            )
            .then((result) => {
                if (result.success) {
                    localStorage.setItem(
                        "ahspSuccessAlert",
                        result.message ||
                            "Data AHSP berhasil diperbarui. Halaman akan dimuat ulang."
                    );
                    localStorage.setItem(
                        "ahspSuccessTime",
                        new Date().getTime()
                    );
                    btnInner.innerHTML =
                        '<i class="fas fa-save"></i> Simpan Perubahan';
                    btnInner.disabled = false;
                    btnInner.classList.remove("loading-btn");
                    if (shouldReload) {
                        window.location.reload();
                    } else {
                        closeFullFormModal(); // Jika tidak reload, tutup modal
                        // Mungkin perlu panggil fungsi refresh data lain jika ada
                    }
                } else {
                    btnInner.innerHTML =
                        '<i class="fas fa-save"></i> Simpan Perubahan';
                    btnInner.disabled = false;
                    btnInner.classList.remove("loading-btn");
                    window.showErrorToast(
                        result.message ||
                            "Terjadi kesalahan saat memperbarui data."
                    );
                }
            })
            .catch((error) => {
                btnInner.innerHTML =
                    '<i class="fas fa-save"></i> Simpan Perubahan';
                btnInner.disabled = false;
                btnInner.classList.remove("loading-btn");

                // Check if it's a subscription error
                if (error.response && error.response.status === 403 && error.response.data && error.response.data.show_toast) {
                    if (typeof window.showSubscriptionErrorToast === 'function') {
                        window.showSubscriptionErrorToast(error.response.data);
                    } else if (typeof window.showInfoToast === 'function') {
                        window.showInfoToast(
                            error.response.data.message || 'Fitur ini tidak tersedia dalam paket langganan Anda.',
                            'Fitur Terbatas',
                            null,
                            7000,
                            error.response.data.upgrade_url || '/subscriptions'
                        );
                    }
                } else {
                    window.showErrorToast(
                        "Terjadi kesalahan jaringan atau server. Periksa console untuk detail."
                    );
                    console.error("Error updating AHSP:", error.message, error);
                }
            });
    }

    const satuanOptions = ["m", "m2", "m3", "kg", "ltr", "bh", "ls", "unit"];
    const satuanSelect = document.getElementById("satuanAHSP");
    if (satuanSelect) {
        satuanOptions.forEach((option) => {
            const opt = document.createElement("option");
            opt.value = option;
            opt.textContent = option;
            satuanSelect.appendChild(opt);
        });
    }

    function showCustomAlert(message, callback) {
        // Simpan pesan di localStorage agar tetap ada setelah reload
        if (message === "Harga satuan tidak boleh '0'") {
            localStorage.setItem("ahspAlert", message);
            localStorage.setItem("ahspAlertTime", new Date().getTime());
        }
        // Gunakan window.showInfoToast yang dibuat di notifications.js
        window.showInfoToast(message, null, callback, 3000); // Ubah durasi menjadi 3000ms (3 detik)
    }

    // === Fungsi untuk Menyimpan Detail Modal (dari tabel) ===
    window.saveDetail = function () {
        const category = window.currentDetailCategory;
        const tbody = document.getElementById("masterTableBody");
        if (!tbody) return;

        // Pastikan semua input koefisien yang terlihat valid
        const visibleKoefInputs = tbody.querySelectorAll(
            ".koefisien-input:not(.hidden)"
        );
        let allValid = true;

        visibleKoefInputs.forEach((input) => {
            if (!input.checkValidity()) {
                allValid = false;
                input.reportValidity();
            }
        });

        if (!allValid) return;

        const rows = tbody.getElementsByTagName("tr");
        let detailsToAdd = [];
        for (let i = 0; i < rows.length; i++) {
            const checkbox = rows[i].querySelector(".master-checkbox");
            if (checkbox && checkbox.checked) {
                const dataId = checkbox.getAttribute("data-id");
                const koefInput = rows[i].querySelector(
                    `.koefisien-input[data-id="${dataId}"]`
                );

                const koefisien = parseFloat(koefInput.value) || 0;
                if (koefisien <= 0) continue;
                let item = null;
                if (category === "Bahan") {
                    item = (window.bahanItems || []).find(
                        (i) => i.id == dataId
                    );
                } else if (category === "Upah") {
                    item = (window.upahItems || []).find((i) => i.id == dataId);
                } else if (category === "Alat") {
                    item = (window.alatItems || []).find((i) => i.id == dataId);
                }
                if (item) {
                    const hargaDasar =
                        item.harga_bahan || item.harga || item.harga_alat || 0;
                    const itemText =
                        item.uraian_bahan ||
                        item.uraian_tenaga ||
                        item.uraian_alat ||
                        "";
                    const satuan = item.satuan || "";
                    const sumber = item.sumber || "";
                    const hargaSatuan = parseFloat(hargaDasar) * koefisien;
                    detailsToAdd.push({
                        kategori: category,
                        item_id: dataId,
                        item_text: itemText,
                        satuan: satuan,
                        sumber: sumber,
                        harga_dasar: hargaDasar,
                        koefisien: koefisien,
                        harga_satuan: hargaSatuan,
                    });
                }
            }
        }
        // Tambahkan detail yang terpilih ke array global detail
        if (category === "Bahan") {
            window.detailBahan = window.detailBahan
                ? window.detailBahan.concat(detailsToAdd)
                : detailsToAdd;
        } else if (category === "Upah") {
            window.detailUpah = window.detailUpah
                ? window.detailUpah.concat(detailsToAdd)
                : detailsToAdd;
        } else if (category === "Alat") {
            window.detailAlat = window.detailAlat
                ? window.detailAlat.concat(detailsToAdd)
                : detailsToAdd;
        }

        // Jika ada item yang ditambahkan, set sumberAHS menjadi "Empiris"
        if (detailsToAdd.length > 0) {
            // Mark that changes have been made
            window.customerHasMadeChanges = true;

            // Set SumberAHS to "Empiris" when adding AHSP items, HANYA untuk NON-ADMIN
            if (!window.isAdmin) {
                const sumberInput = document.getElementById("sumberAHSP");
                if (sumberInput) {
                    sumberInput.value = "Empiris";
                }
            }
        }

        updateDetailTable(category);
        closeDetailModal();
    };

    // === Fungsi untuk Hapus Detail ===
    window.deleteDetail = function (category, index) {
        // Mark that changes have been made
        window.customerHasMadeChanges = true;

        // Set SumberAHS to "Empiris" when deleting any AHSP item, HANYA untuk NON-ADMIN
        // Only change the sumber field if we're dealing with an AHSP item, not a master item
        if (!window.isAdmin) {
            const sumberInput = document.getElementById("sumberAHSP");
            if (sumberInput) {
                // Only set sumber to "Empiris" if we're not dealing with master data items
                // This ensures we preserve the source data for master items
                const isAhspItem = true; // AHSP items are the ones being displayed in the interface
                if (isAhspItem) {
                    sumberInput.value = "Empiris";
                }
            }
        }

        if (category === "Bahan") {
            window.detailBahan.splice(index, 1);
        } else if (category === "Upah") {
            window.detailUpah.splice(index, 1);
        } else if (category === "Alat") {
            window.detailAlat.splice(index, 1);
        }

        updateDetailTable(category);
    };

    // === Fungsi untuk menangani Hapus Detail dengan konfirmasi ===
    window.handleDetailDelete = function (category, index) {
        // Tampilkan konfirmasi sebelum menghapus
        if (window.showCustomConfirm) {
            window.showCustomConfirm(
                "Apakah Anda yakin ingin menghapus item ini?",
                function () {
                    // Jika pengguna mengkonfirmasi, panggil fungsi deleteDetail
                    window.deleteDetail(category, index);

                    // Sembunyikan menu konteks jika masih terbuka
                    const contextMenu = document.getElementById(
                        "detail-context-menu"
                    );
                    if (contextMenu) {
                        contextMenu.style.display = "none";
                    }
                }
            );
        } else if (window.showConfirm) {
            // Alternatif jika showCustomConfirm tidak tersedia
            window.showConfirm(
                "Apakah Anda yakin ingin menghapus item ini?",
                function () {
                    window.deleteDetail(category, index);

                    // Sembunyikan menu konteks jika masih terbuka
                    const contextMenu = document.getElementById(
                        "detail-context-menu"
                    );
                    if (contextMenu) {
                        contextMenu.style.display = "none";
                    }
                }
            );
        } else {
            // Fallback ke confirm bawaan browser jika tidak ada fungsi khusus
            if (confirm("Apakah Anda yakin ingin menghapus item ini?")) {
                window.deleteDetail(category, index);

                // Sembunyikan menu konteks jika masih terbuka
                const contextMenu = document.getElementById(
                    "detail-context-menu"
                );
                if (contextMenu) {
                    contextMenu.style.display = "none";
                }
            }
        }
    };

    // === Handle Edit Detail Item ===
    window.handleDetailEdit = function (category, index) {
        let detailItem;
        if (category === "Bahan") {
            detailItem = window.detailBahan[index];
        } else if (category === "Upah") {
            detailItem = window.detailUpah[index];
        } else if (category === "Alat") {
            detailItem = window.detailAlat[index];
        }

        if (!detailItem) {
            showCustomAlert("Item tidak ditemukan.");
            return;
        }

        // Store current edit state
        window.currentEditingDetail = {
            category: category,
            index: index,
            itemId: detailItem.item_id,
        };

        // Customer has made changes
        if (!window.isAdmin) {
            window.customerHasMadeChanges = true;
        }

        // Open edit item modal
        openEditItemModal(detailItem, category, index, detailItem.item_id);
    };

    // === Edit Item Modal Functions ===
    window.openEditItemModal = function (item, category, index, itemId) {
        // Set values in the form
        document.getElementById("editItemId").value = itemId;
        document.getElementById("editItemCategory").value = category;
        document.getElementById("editItemIndex").value = index;
        document.getElementById("editItemText").value = item.item_text;

        // Store original values for change detection
        window.originalItemText = item.item_text;
        window.originalHargaDasar = parseFloat(item.harga_dasar).toFixed(2);

        // Set satuan in both the hidden input and the label
        document.getElementById("editItemSatuan").value = item.satuan;
        document.getElementById("editItemSatuanLabel").innerText =
            item.satuan || "-";

        document.getElementById("editItemHarga").value = parseFloat(
            item.harga_dasar
        ).toFixed(2);

        // Set the title based on category
        document.getElementById("editItemTitle").innerText = `Edit ${category}`;

        // Show the modal
        const modal = document.getElementById("editItemModal");
        if (modal) modal.classList.remove("hidden");
    };

    window.closeEditItemModal = function () {
        const modal = document.getElementById("editItemModal");
        if (modal) modal.classList.add("hidden");
    };

    window.saveItemEdit = function () {
        // Get form values
        const category = document.getElementById("editItemCategory").value;
        const index = parseInt(document.getElementById("editItemIndex").value);
        const itemText = document.getElementById("editItemText").value;
        const satuan = document.getElementById("editItemSatuan").value;
        const hargaDasar = parseFloat(
            document.getElementById("editItemHarga").value
        );

        // Check if there are any changes
        const isItemTextChanged = window.originalItemText !== itemText;
        const isHargaDasarChanged =
            window.originalHargaDasar !==
            document.getElementById("editItemHarga").value;

        if (!isItemTextChanged && !isHargaDasarChanged) {
            showCustomAlert("Tidak ada perubahan data yang dilakukan");
            return;
        }

        // Validate inputs
        if (!itemText.trim()) {
            showCustomAlert("Uraian tidak boleh kosong");
            return;
        }

        if (!satuan.trim()) {
            showCustomAlert("Satuan tidak boleh kosong");
            return;
        }

        if (isNaN(hargaDasar) || hargaDasar <= 0) {
            showCustomAlert("Harga dasar harus lebih dari 0");
            return;
        }

        // Update the item in the appropriate array
        let detailItem;
        if (
            category === "Bahan" &&
            window.detailBahan &&
            window.detailBahan[index]
        ) {
            detailItem = window.detailBahan[index];
        } else if (
            category === "Upah" &&
            window.detailUpah &&
            window.detailUpah[index]
        ) {
            detailItem = window.detailUpah[index];
        } else if (
            category === "Alat" &&
            window.detailAlat &&
            window.detailAlat[index]
        ) {
            detailItem = window.detailAlat[index];
        }

        if (!detailItem) {
            showCustomAlert("Item tidak ditemukan");
            return;
        }

        // Calculate harga satuan - using detailItem's koefisien
        const hargaSatuan = hargaDasar * detailItem.koefisien;

        if (detailItem) {
            // Store original koefisien to check if it changed
            const originalKoefisien = detailItem.koefisien;
            const originalItemText = detailItem.item_text;

            detailItem.item_text = itemText;
            detailItem.satuan = satuan;
            detailItem.harga_dasar = hargaDasar;
            // No longer modifying koefisien since the field is removed
            detailItem.harga_satuan = hargaSatuan;

            // For customer users who have made changes, mark as having made changes
            if (!window.isAdmin) {
                window.customerHasMadeChanges = true;

                // Jika nama item/uraian berubah, set sumber menjadi "Empiris"
                if (originalItemText !== itemText) {
                    // Ubah sumber di form
                    const sumberInput = document.getElementById("sumberAHSP");
                    if (sumberInput) {
                        sumberInput.value = "Empiris";
                    }
                    // Ubah sumber di item detail jika properti ini ada
                    if (detailItem.hasOwnProperty("sumber")) {
                        detailItem.sumber = "Empiris";
                    }
                }
            } else {
                // Ini adalah admin, jangan ubah sumberAHS
                window.customerHasMadeChanges = true;
            }

            // Update the table display
            updateDetailTable(category);

            // Close the modal
            closeEditItemModal();

            // Save changes to the server - Create new record in database
            saveItemToServer(detailItem, category, index);

            // Show success message
            showCustomAlert("Item berhasil diperbarui");
        } else {
            showCustomAlert("Terjadi kesalahan saat memperbarui item");
        }
    };

    // Fungsi untuk menyimpan perubahan item ke server (sebagai data baru)
    function saveItemToServer(item, category, index) {
        // Get CSRF token
        const token = document
            .querySelector('meta[name="csrf-token"]')
            ?.getAttribute("content");
        if (!token) {
            console.error("CSRF token not found");
            return;
        }

        // 1. First add a NEW master item to the database
        let masterUrl;
        let masterData = {};

        // Prepare data based on category
        if (category === "Bahan") {
            masterUrl = "/bahan";
            masterData = {
                uraian_bahan: item.item_text,
                satuan: item.satuan,
                harga_bahan: item.harga_dasar,
                create_new: true, // Flag to create new record
            };
        } else if (category === "Upah") {
            masterUrl = "/upah";
            masterData = {
                uraian_tenaga: item.item_text,
                satuan: item.satuan,
                harga: item.harga_dasar,
                create_new: true, // Flag to create new record
            };
        } else if (category === "Alat") {
            masterUrl = "/alat";
            masterData = {
                uraian_alat: item.item_text,
                satuan: item.satuan,
                harga_alat: item.harga_dasar,
                create_new: true, // Flag to create new record
            };
        } else {
            console.error("Invalid category for server save");
            return;
        }

        // Send request to create new master item
        fetch(masterUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": token,
                Accept: "application/json",
            },
            body: JSON.stringify(masterData),
        })
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }
                return response.json();
            })
            .then((result) => {
                console.log("New master item created:", result);

                if (result.success && result.resource) {
                    // Dapatkan ID baru dari item master yang baru dibuat
                    const newItemId = result.resource.id;
                    const originalItemId = item.item_id;

                    // Update item ID di memori untuk tampilan UI
                    item.item_id = newItemId;

                    console.log(
                        "Item master baru berhasil dibuat dengan ID:",
                        newItemId
                    );
                    console.log("Tidak melakukan update ke tabel ahsp_details");

                    // Tidak ada request ke server untuk memperbarui ahsp_details
                }
            })
            .catch((error) => {
                console.error("Error saving item to server:", error);
            });
    }

    // Fungsi untuk memeriksa apakah inputKategoriModal sedang terbuka
    function isInputKategoriModalOpen() {
        const inputKategoriModal =
            document.getElementById("inputKategoriModal");
        return (
            inputKategoriModal &&
            !inputKategoriModal.classList.contains("hidden")
        );
    }

    // === Modal Detail Input (Tabel dengan Checkbox & Input Koefisien) ===
    window.openDetailModal = function (category) {
        window.currentDetailCategory = category;
        const labelEl = document.getElementById("selectedCategoryLabel");
        if (labelEl) labelEl.innerText = category;
        const searchInput = document.getElementById("detailSearch");
        if (searchInput) searchInput.value = "";

        // Update tombol tambah dengan text sesuai kategori
        const tambahButtonText = document.getElementById("tambahButtonText");
        if (tambahButtonText) {
            if (category === "Bahan") {
                tambahButtonText.innerText = "Tambah Bahan";
            } else if (category === "Upah") {
                tambahButtonText.innerText = "Tambah Upah";
            } else if (category === "Alat") {
                tambahButtonText.innerText = "Tambah Alat";
            } else {
                tambahButtonText.innerText = "Tambahkan";
            }
        }

        populateMasterTable(category);
        openModal("detailModal");
    };

    window.closeDetailModal = function () {
        closeModal("detailModal");
        // Reset pagination when modal is closed
        window.detailModalPagination.currentPage = 1;
        window.detailModalPagination.filteredData = [];
        window.currentDetailModalData = [];
        const searchInput = document.getElementById("detailSearch");
        if (searchInput) searchInput.value = "";
        const paginationControls = document.getElementById(
            "detailModalPaginationControls"
        );
        if (paginationControls) paginationControls.innerHTML = "";
    };

    // === Clear Semua Detail ===
    window.clearAllDetailTables = function () {
        window.detailBahan = [];
        window.detailUpah = [];
        window.detailAlat = [];
        updateDetailTable("Bahan");
        updateDetailTable("Upah");
        updateDetailTable("Alat");
    };

    // === Fungsi untuk Mengisi Tabel Master pada Modal Detail ===
    function populateMasterTable(category) {
        let items = [];
        if (category === "Bahan") {
            items = window.bahanItems || [];
        } else if (category === "Upah") {
            items = window.upahItems || [];
        } else if (category === "Alat") {
            items = window.alatItems || [];
        }

        // Urutkan items agar data terbaru muncul di awal (urutan pertama)
        // Asumsikan bahwa data terbaru memiliki ID yang lebih besar
        items.sort((a, b) => {
            return b.id - a.id; // Urutkan berdasarkan ID secara descending (terbesar ke terkecil)
        });

        window.currentDetailModalData = items; // Store all items for the current category
        window.detailModalPagination.filteredData = items; // Initially, filtered data is all data
        window.detailModalPagination.totalPages = Math.ceil(
            window.currentDetailModalData.length /
                window.detailModalPagination.itemsPerPage
        );
        if (window.detailModalPagination.totalPages === 0)
            window.detailModalPagination.totalPages = 1;
        window.detailModalPagination.currentPage = 1; // Reset to page 1

        displayDetailModalPage(window.detailModalPagination.currentPage);
        updateDetailModalPaginationControls();

        // Fitur pencarian - moved setup of listener outside of repeated rendering
        // The actual filtering logic will be in displayDetailModalPage or a dedicated filter function
    }

    // Function to display a page of Detail Modal data
    function displayDetailModalPage(page) {
        const tbody = document.getElementById("masterTableBody");
        if (!tbody) {
            console.error("Elemen #masterTableBody tidak ditemukan.");
            return;
        }
        tbody.innerHTML = "";

        const searchInput = document.getElementById("detailSearch");
        const filter = searchInput ? searchInput.value.toLowerCase() : "";

        let dataToPaginate = window.currentDetailModalData;
        if (filter !== "") {
            dataToPaginate = window.currentDetailModalData.filter((item) => {
                const itemName = (
                    item.uraian_bahan ||
                    item.uraian_tenaga ||
                    item.uraian_alat ||
                    ""
                ).toLowerCase();
                const satuan = (item.satuan || "").toLowerCase();
                const sumber = (item.sumber || "").toLowerCase();
                return (
                    itemName.includes(filter) ||
                    satuan.includes(filter) ||
                    sumber.includes(filter)
                );
            });

            // Pertahankan urutan data terbaru di atas setelah filter
            // Data sudah diurut di fungsi populateMasterTable

            window.detailModalPagination.totalPages = Math.ceil(
                dataToPaginate.length /
                    window.detailModalPagination.itemsPerPage
            );
            if (window.detailModalPagination.totalPages === 0)
                window.detailModalPagination.totalPages = 1;
            // If current page is out of bounds due to filtering, reset to 1
            if (page > window.detailModalPagination.totalPages) {
                page = 1;
                window.detailModalPagination.currentPage = 1;
            }
        } else {
            window.detailModalPagination.totalPages = Math.ceil(
                window.currentDetailModalData.length /
                    window.detailModalPagination.itemsPerPage
            );
            if (window.detailModalPagination.totalPages === 0)
                window.detailModalPagination.totalPages = 1;
        }

        window.detailModalPagination.filteredData = dataToPaginate; // Update filtered data reference

        const startIndex =
            (page - 1) * window.detailModalPagination.itemsPerPage;
        const endIndex = startIndex + window.detailModalPagination.itemsPerPage;
        const pageData = dataToPaginate.slice(startIndex, endIndex);

        pageData.forEach(function (item, index) {
            const actualIndex = startIndex + index; // For numbering if needed
            const itemName =
                item.uraian_bahan ||
                item.uraian_tenaga ||
                item.uraian_alat ||
                "";
            const satuan = item.satuan || "";
            const harga =
                item.harga_bahan || item.harga || item.harga_alat || 0;
            const sumber = item.sumber || "";
            const tr = document.createElement("tr");
            tr.innerHTML = `
                <td class="p-2 border text-center">
                    <input type="checkbox" class="master-checkbox" data-id="${
                        item.id
                    }">
                </td>
                <td class="p-2 border">${actualIndex + 1}</td>
                <td class="p-2 border px-4 py-4 max-w-[300px] truncate hover:max-w-none">${itemName}</td>
                <td class="p-2 border max-w-5">${satuan}</td>
                <td class="p-2 border" data-hargasatuan="${parseFloat(
                    harga
                ).toFixed(2)}">
                    <span class="float-left">Rp.</span>
                    <span class="float-right">${parseFloat(
                        harga
                    ).toLocaleString("id-ID", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                    })}</span>
                </td>
                <td class="p-2 border">${sumber}</td>
                <td class="p-2 border min-w-10 min-w-15">
                    <input type="number" step="0.01" min="0.01" required class="koefisien-input hidden editable w-full p-1 border" data-id="${
                        item.id
                    }" placeholder="Masukkan Koef">
                </td>
            `;
            tbody.appendChild(tr);
        });

        document
            .querySelectorAll(".master-checkbox")
            .forEach(function (checkbox) {
                checkbox.addEventListener("change", function () {
                    const dataId = this.getAttribute("data-id");
                    const koefInput = document.querySelector(
                        `.koefisien-input[data-id="${dataId}"]`
                    );
                    if (this.checked) {
                        koefInput.classList.remove("hidden");
                    } else {
                        koefInput.classList.add("hidden");
                        koefInput.value = "";
                    }
                });
            });
        updateDetailModalPaginationControls(); // Update controls after displaying page, especially if filtered
    }

    // Function to update Detail Modal pagination controls
    function updateDetailModalPaginationControls() {
        const paginationContainer = document.getElementById(
            "detailModalPaginationControls"
        );
        if (!paginationContainer) return;

        const { currentPage, totalPages } = window.detailModalPagination;

        if (totalPages <= 1) {
            paginationContainer.innerHTML = "";
            return;
        }

        paginationContainer.innerHTML = `
            <div class="w-full flex items-center justify-between mt-4">
                <span class="text-sm text-gray-700 dark:text-gray-400 mr-3">
                    Hal <span class="font-semibold">${currentPage}</span> dari <span class="font-semibold">${totalPages}</span>
                </span>
                <div class="flex space-x-2">
                    <button
                        ${currentPage === 1 ? "disabled" : ""}
                        onclick="navigateToDetailModalPage(${currentPage - 1})"
                        class="px-3 py-1 ${
                            currentPage === 1
                                ? "bg-gray-300 dark:bg-gray-700 cursor-not-allowed"
                                : "bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80"
                        } text-white rounded-md transition-all duration-200">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button
                        ${currentPage === totalPages ? "disabled" : ""}
                        onclick="navigateToDetailModalPage(${currentPage + 1})"
                        class="px-3 py-1 ${
                            currentPage === totalPages
                                ? "bg-gray-300 dark:bg-gray-700 cursor-not-allowed"
                                : "bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80"
                        } text-white rounded-md transition-all duration-200">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // Function to navigate Detail Modal pages
    function navigateToDetailModalPage(page) {
        if (page < 1 || page > window.detailModalPagination.totalPages) return;
        window.detailModalPagination.currentPage = page;
        displayDetailModalPage(page); // This will re-filter if needed and display
        // updateDetailModalPaginationControls() is called within displayDetailModalPage
    }
    window.navigateToDetailModalPage = navigateToDetailModalPage;

    // Setup search listener for detailModal once
    const detailSearchInput = document.getElementById("detailSearch");
    if (detailSearchInput) {
        detailSearchInput.addEventListener("input", function () {
            // When search input changes, re-display page 1 with the filter
            displayDetailModalPage(1);
        });
    }

    // === Update Tabel Detail Summary ===
    window.updateDetailTable = function (category) {
        let tbody, totalCell, dataArray;
        if (category === "Bahan") {
            tbody = document.getElementById("tempBahan");
            totalCell = document.getElementById("totalBahanCell");
            dataArray = window.detailBahan || [];
        } else if (category === "Upah") {
            tbody = document.getElementById("tempUpah");
            totalCell = document.getElementById("totalUpahCell");
            dataArray = window.detailUpah || [];
        } else if (category === "Alat") {
            tbody = document.getElementById("tempAlat");
            totalCell = document.getElementById("totalAlatCell");
            dataArray = window.detailAlat || [];
        }
        if (!tbody) return;
        tbody.innerHTML = "";
        let total = 0;
        dataArray.forEach(function (detail, index) {
            total += parseFloat(detail.harga_satuan);
            tbody.innerHTML += `
                <tr class="detail-row" data-index="${index}" data-kategori="${category}">
                    <td class="p-2 border text-center w-3">${index + 1}</td>
                    <td class="p-2 border px-4 py-4 max-w-[100px] truncate hover:max-w-none">${
                        detail.item_text
                    }</td>
                    <td class="p-2 border text-center w-4">
                        <input type="number" step="0.01" class="editable-detail-koefisien w-full p-1 border text-center" value="${
                            detail.koefisien
                        }" data-index="${index}" data-kategori="${category}">
                    </td>
                    <td class="p-2 border w-auto">${detail.satuan}</td>
                    <td class="p-2 border" data-hargasatuan="${parseFloat(
                        detail.harga_dasar
                    ).toFixed(2)}">
                        <span class="float-left">Rp.</span>
                        <span class="float-right">${Number(
                            detail.harga_dasar
                        ).toLocaleString("id-ID", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                        })}</span>
                    </td>
                    <td class="p-2 border" data-hargasatuan="${parseFloat(
                        detail.harga_satuan
                    ).toFixed(2)}">
                        <span class="float-left">Rp.</span>
                        <span class="float-right">${Number(
                            detail.harga_satuan
                        ).toLocaleString("id-ID", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                        })}</span>
                    </td>
                    <td class="p-2 border text-center w-10">
                        <div class="flex justify-center space-x-2">
                            <button type="button" class="editDetailBtn bg-light-accent hover:bg-light-accent/80 text-white px-2 py-1 rounded" onclick="handleDetailEdit('${category}', ${index})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="deleteDetailBtn bg-red-500 hover:bg-red-700 text-white px-2 py-1 rounded" onclick="handleDetailDelete('${category}', ${index})">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        if (totalCell) {
            // Format total dengan format Indonesia (koma sebagai desimal)
            const formattedTotal = total
                .toLocaleString("id-ID", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                })
                .replace(".", ",");

            // Update dengan format HTML yang benar (Rp. float-left dan total float-right)
            totalCell.innerHTML = `
                <span class="float-left">Rp.</span>
                <span class="float-right">${formattedTotal}</span>
            `;
        }
        updateGrandTotal();

        // Bind event listener untuk input koefisien agar perubahan langsung di-update
        const editableInputs = document.querySelectorAll(
            ".editable-detail-koefisien"
        );
        editableInputs.forEach(function (input) {
            input.addEventListener("change", function () {
                const idx = this.getAttribute("data-index");
                const cat = this.getAttribute("data-kategori");
                const newKoef = parseFloat(this.value);
                if (!this.value.trim() || newKoef <= 0) {
                    showCustomAlert(
                        "Koefisien tidak boleh kosong atau kurang dari atau sama dengan 0"
                    );
                    this.value = "";
                    return;
                }

                // Mark that changes have been made
                window.customerHasMadeChanges = true;

                let detailItem;
                if (cat === "Bahan") {
                    detailItem = window.detailBahan[idx];
                } else if (cat === "Upah") {
                    detailItem = window.detailUpah[idx];
                } else if (cat === "Alat") {
                    detailItem = window.detailAlat[idx];
                }
                if (detailItem) {
                    detailItem.koefisien = newKoef;
                    detailItem.harga_satuan =
                        parseFloat(detailItem.harga_dasar) * newKoef;
                    updateDetailTable(cat);

                    // For customer users who have made changes, mark as having made changes
                    if (!window.isAdmin) {
                        const sumberInput =
                            document.getElementById("sumberAHSP");
                        if (
                            sumberInput &&
                            (cat === "Bahan" ||
                                cat === "Upah" ||
                                cat === "Alat")
                        ) {
                            sumberInput.value = "Empiris";
                        }
                    } else {
                        // Ini adalah admin, jangan ubah sumberAHS
                        window.customerHasMadeChanges = true;
                    }
                }
            });
        });
    };

    function updateGrandTotal() {
        const totalBahan = window.detailBahan
            ? window.detailBahan.reduce(
                  (sum, detail) => sum + parseFloat(detail.harga_satuan),
                  0
              )
            : 0;
        const totalUpah = window.detailUpah
            ? window.detailUpah.reduce(
                  (sum, detail) => sum + parseFloat(detail.harga_satuan),
                  0
              )
            : 0;
        const totalAlat = window.detailAlat
            ? window.detailAlat.reduce(
                  (sum, detail) => sum + parseFloat(detail.harga_satuan),
                  0
              )
            : 0;
        const subtotal = totalBahan + totalUpah + totalAlat;
        const overheadPercent =
            parseFloat(document.getElementById("overheadInput").value) || 0;
        const overhead = subtotal * (overheadPercent / 100);
        const grandTotal = subtotal + overhead;

        // Format dengan format Indonesia (koma sebagai desimal)
        const formattedSubtotal = subtotal
            .toLocaleString("id-ID", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            })
            .replace(".", ",");

        const formattedOverhead = overhead
            .toLocaleString("id-ID", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            })
            .replace(".", ",");

        const formattedGrandTotal = grandTotal
            .toLocaleString("id-ID", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            })
            .replace(".", ",");

        // Update dengan format HTML yang benar (Rp. float-left dan total float-right)
        const subtotalCell = document.getElementById("subtotalCell");
        if (subtotalCell) {
            subtotalCell.innerHTML = `
                <span class="float-left">Rp.</span>
                <span class="float-right">${formattedSubtotal}</span>
            `;
        }

        const overheadCell = document.getElementById("overheadCell");
        if (overheadCell) {
            overheadCell.innerHTML = `
                <span class="float-left">Rp.</span>
                <span class="float-right">${formattedOverhead}</span>
            `;
        }

        const grandTotalCell = document.getElementById("grandTotalCell");
        if (grandTotalCell) {
            grandTotalCell.innerHTML = `
                <span class="float-left">Rp.</span>
                <span class="float-right">${formattedGrandTotal}</span>
            `;
        }

        const grandTotalLabel = document.getElementById("grandTotalLabel");
        if (grandTotalLabel) {
            grandTotalLabel.innerHTML = `
                <span class="float-left">Rp.</span>
                <span class="float-right">${formattedGrandTotal}</span>
            `;
        }

        // Update totalBahanCell dan totalAlatCell jika belum diupdate
        const totalBahanCell = document.getElementById("totalBahanCell");
        if (totalBahanCell && !totalBahanCell.querySelector(".float-left")) {
            const formattedTotalBahan = totalBahan
                .toLocaleString("id-ID", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                })
                .replace(".", ",");

            totalBahanCell.innerHTML = `
                <span class="float-left">Rp.</span>
                <span class="float-right">${formattedTotalBahan}</span>
            `;
        }

        const totalAlatCell = document.getElementById("totalAlatCell");
        if (totalAlatCell && !totalAlatCell.querySelector(".float-left")) {
            const formattedTotalAlat = totalAlat
                .toLocaleString("id-ID", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                })
                .replace(".", ",");

            totalAlatCell.innerHTML = `
                <span class="float-left">Rp.</span>
                <span class="float-right">${formattedTotalAlat}</span>
            `;
        }
    }

    const overheadInput = document.getElementById("overheadInput");
    if (overheadInput) {
        overheadInput.addEventListener("input", function () {
            updateGrandTotal();
        });
    }

    function numberFormat(value) {
        return parseFloat(value).toLocaleString("id-ID", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        });
    }

    // === Fungsi Helper: Menghitung Grand Total dan mengembalikan nilainya ===
    function computeGrandTotal() {
        const totalBahan = window.detailBahan
            ? window.detailBahan.reduce(
                  (sum, detail) => sum + parseFloat(detail.harga_satuan),
                  0
              )
            : 0;
        const totalUpah = window.detailUpah
            ? window.detailUpah.reduce(
                  (sum, detail) => sum + parseFloat(detail.harga_satuan),
                  0
              )
            : 0;
        const totalAlat = window.detailAlat
            ? window.detailAlat.reduce(
                  (sum, detail) => sum + parseFloat(detail.harga_satuan),
                  0
              )
            : 0;
        const subtotal = totalBahan + totalUpah + totalAlat;
        const overheadPercent =
            parseFloat(document.getElementById("overheadInput").value) || 0;
        const overhead = subtotal * (overheadPercent / 100);
        return subtotal + overhead;
    }
});
