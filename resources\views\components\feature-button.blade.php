@props([
    'href' => null,
    'type' => 'button',
    'onclick' => null,
    'feature' => null,
    'title' => '',
    'icon' => '',
    'showTooltip' => true,
    'class' => null,
    'disabledClass' => 'opacity-50 cursor-not-allowed bg-gray-400 dark:bg-gray-600'
])

@php
    $hasAccess = $feature ? hasFeatureAccess($feature) : true;
    
    $baseClass = 'inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-150';
    $finalClass = $class ?: $baseClass;
    
    if (!$hasAccess) {
        $finalClass .= ' ' . $disabledClass;
    }
@endphp

@if($hasAccess)
    @if($href)
        <a href="{{ $href }}" class="{{ $finalClass }}" @if($showTooltip && $title) title="{{ $title }}" @endif>
            @if($icon)
                <i class="{{ $icon }} mr-2"></i>
            @endif
            {{ $slot }}
        </a>
    @else
        <button type="{{ $type }}" 
                class="{{ $finalClass }}" 
                @if($onclick) onclick="{{ $onclick }}" @endif
                @if($showTooltip && $title) title="{{ $title }}" @endif>
            @if($icon)
                <i class="{{ $icon }} mr-2"></i>
            @endif
            {{ $slot }}
        </button>
    @endif
@else
    <div class="{{ $finalClass }}"
         data-feature="{{ $feature }}"
         @if($showTooltip)
             title="Fitur ini tidak tersedia dalam paket langganan Anda. Silakan upgrade paket untuk mengakses fitur ini."
         @endif
         onclick="event.preventDefault(); if(typeof window.subscriptionChecker !== 'undefined') { window.subscriptionChecker.showFeatureRestrictedToast({{ json_encode($feature) }}); }">
        @if($icon)
            <i class="{{ $icon }} mr-2"></i>
        @endif
        {{ $slot }}
        <i class="fas fa-lock ml-2 text-gray-400"></i>
    </div>
@endif
