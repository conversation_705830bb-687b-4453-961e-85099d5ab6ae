@extends('layouts.app')

@section('styles')
@endsection

@section('content')
    <div class="container mx-auto max-w-6xl p-6 progres-container">
    <div class="flex justify-between items-center mb-4">
        @if ($skala == 'minggu')
            <button type="button" id="btnTambahProgres"
                class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg flex items-center whitespace-nowrap shadow-sm hover:shadow transform hover:scale-105 transition-all duration-150">
                <i class="fas fa-plus-circle mr-2"></i> Tambah Progres
            </button>
        @else
            <div></div>
        @endif
        <div class="flex items-center space-x-2">
            <div class="mr-2 text-base font-semibold text-gray-700 dark:text-gray-200">
                Periode: {{ \Carbon\Carbon::parse($bulanTahun.'-01')->translatedFormat('F Y') }}
            </div>
            <!-- Fitur Search -->
            <div class="flex items-center space-x-2 ml-4">
                <label for="searchProgres" class="text-sm font-medium text-light-text dark:text-dark-text">Cari:</label>
                <input type="text" id="searchProgres" placeholder="Cari..."
                       class="border px-3 py-1 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                       oninput="filterProgresTable()">
            </div>
        </div>
    </div>
    <div class="mb-4 flex justify-center">
        <h2 class="text-xl font-bold text-light-text dark:text-dark-text text-center">Progres Proyek: {{ $project->name }}</h2>
    </div>

    <form action="{{ route('progres.update-batch') }}" method="POST">
        @csrf
        <input type="hidden" name="project_id" value="{{ $project->id }}">

            <input type="hidden" name="periode" value="Minggu_ke_{{ $currentPeriod }}">
            <input type="hidden" name="skala" value="{{ $skala }}">
            <input type="hidden" name="current_period" value="{{ $currentPeriod }}">
            @if (isset($tanggalMulai))
                <input type="hidden" name="tanggal_mulai" value="{{ $tanggalMulai }}">
            @endif
            @if (isset($tanggalSelesai))
                <input type="hidden" name="tanggal_selesai" value="{{ $tanggalSelesai }}">
            @endif

            @if ($skala == 'bulan')
                <div
                    class="mb-4 bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md p-3 text-blue-800 dark:text-blue-200">
                    <p class="flex items-center">
                        <i class="fas fa-info-circle mr-2"></i>
                        <span>Dalam skala Bulan, data progres merupakan akumulasi dari minggu-minggu dalam bulan tersebut.
                            Untuk mengubah progres, silakan beralih ke skala Minggu.</span>
                    </p>
                </div>
            @endif

        <div class="overflow-y-auto max-h-[530px]">
            <table class="min-w-full bg-white dark:bg-gray-800 border text-sm">
                <thead class="bg-green-200 dark:bg-blue-800 sticky top-0 z-10 text-gray-800 dark:text-gray-200">
                    <tr>
                        <th rowspan="4" class="border py-2 px-4">No</th>
                        <th colspan="6" class="border py-2 px-4">Rencana Anggaran Biaya</th>
                        <th colspan="9" class="border py-2 px-4">Realisasi</th>
                    </tr>
                    <tr>
                        <th rowspan="3" class="border py-2 px-4">Uraian Pekerjaan</th>
                        <!-- Sub Header Rab Kontrak -->
                        <th rowspan="3" class="border py-2 px-4">Volume</th>
                        <th rowspan="3" class="border py-2 px-4">Satuan</th>
                        <th rowspan="3" class="border py-2 px-4">Harga Satuan</th>
                        <th rowspan="3" class="border py-2 px-4">Jumlah Harga</th>
                        <th rowspan="3" class="border py-2 px-4">Bobot</th>
                    </tr>
                    <tr>
                        <!-- Sub Header Progres -->
                            <th colspan="3" class="border py-2 px-4">s/d {{ $skala == 'minggu' ? 'Minggu' : 'Bulan' }}
                                lalu</th>
                            <th colspan="3" class="border py-2 px-4">{{ $skala == 'minggu' ? 'Minggu' : 'Bulan' }} ini
                            </th>
                            <th colspan="3" class="border py-2 px-4">s/d {{ $skala == 'minggu' ? 'Minggu' : 'Bulan' }}
                                ini</th>
                    </tr>
                    <tr>
                        <th class="border py-2 px-4">Volume</th>
                        <th class="border py-2 px-4">Jumlah Harga</th>
                        <th class="border py-2 px-4">Bobot</th>
                        <th class="border py-2 px-4">Volume</th>
                        <th class="border py-2 px-4">Jumlah Harga</th>
                        <th class="border py-2 px-4">Bobot</th>
                        <th class="border py-2 px-4">Volume</th>
                        <th class="border py-2 px-4">Jumlah Harga</th>
                        <th class="border py-2 px-4">Bobot</th>
                    </tr>
                </thead>
                <tbody>
                    @php
                        $no = 1;
                        $totalJumlahHarga = 0;
                        $totalRABBobot = 0;
                        $totalSdBulanLaluVolume = 0;
                        $totalSdBulanLaluJumlahHarga = 0;
                        $totalSdBulanLaluBobot = 0;
                        $totalBulanIniVolume = 0;
                        $totalBulanIniJumlahHarga = 0;
                        $totalBulanIniBobot = 0;
                        $totalSdBulanIniVolume = 0;
                        $totalSdBulanIniJumlahHarga = 0;
                        $totalSdBulanIniBobot = 0;
                    @endphp

                        @if ($kategoriPekerjaans->isEmpty())
                        <tr>
                            <td colspan="16" class="border py-2 px-4 text-center text-gray-600 dark:text-gray-400 whitespace-nowrap overflow-hidden truncate">Belum ada data progres untuk ditampilkan</td>
                        </tr>
                    @else
                        @php
                            // Hitung total nilai kontrak terlebih dahulu untuk menghitung bobot
                            $totalNilaiKontrak = 0;
                                foreach ($kategoriPekerjaans as $kategori) {
                                    foreach ($kategori->items as $item) {
                                    $totalNilaiKontrak += $item->harga_total;
                                }
                            }
                        @endphp

                            @foreach ($kategoriPekerjaans as $catIndex => $kategori)
                            <tr class="bg-blue-100 dark:bg-blue-900/50 font-semibold text-gray-800 dark:text-gray-200">
                                <td class="border py-2 px-4 text-center whitespace-nowrap overflow-hidden truncate max-w-[40px]">{{ chr(65 + $catIndex) }}</td>
                                <td colspan="15" class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate">{{ $kategori->nama_kategori }}</td>
                            </tr>

                                @foreach ($kategori->items as $itemIndex => $item)
                                @php
                                    // Data RAB
                                    $volume = $item->volume;
                                    $satuan = $item->satuan;
                                    $hargaSatuan = $item->harga_satuan;
                                    $jumlahHarga = $item->harga_total;

                                    // Hitung bobot item dalam RAB
                                        $itemBobot =
                                            $totalNilaiKontrak > 0 ? ($jumlahHarga / $totalNilaiKontrak) * 100 : 0;
                                    $totalRABBobot += $itemBobot;

                                    $totalJumlahHarga += $jumlahHarga;

                                        // Data progres minggu lalu jika ada
                                        $sdBulanLaluVolume = isset($progresItems[$item->id]['minggu_lalu'])
                                            ? $progresItems[$item->id]['minggu_lalu'][0]->volume_realisasi
                                            : 0;
                                        $sdBulanLaluJumlahHarga = isset($progresItems[$item->id]['minggu_lalu'])
                                            ? $progresItems[$item->id]['minggu_lalu'][0]->jumlah_harga
                                            : 0;
                                        $sdBulanLaluBobot = isset($progresItems[$item->id]['minggu_lalu'])
                                            ? $progresItems[$item->id]['minggu_lalu'][0]->bobot
                                            : 0;

                                        // Data progres minggu ini jika ada
                                        $bulanIniVolume = isset($progresItems[$item->id]['minggu_ini'])
                                            ? $progresItems[$item->id]['minggu_ini'][0]->volume_realisasi
                                            : 0;
                                        $bulanIniJumlahHarga = isset($progresItems[$item->id]['minggu_ini'])
                                            ? $progresItems[$item->id]['minggu_ini'][0]->jumlah_harga
                                            : 0;
                                        $bulanIniBobot = isset($progresItems[$item->id]['minggu_ini'])
                                            ? $progresItems[$item->id]['minggu_ini'][0]->bobot
                                            : 0;

                                    // Data s/d bulan ini (penjumlahan)
                                    $sdBulanIniVolume = $sdBulanLaluVolume + $bulanIniVolume;
                                    $sdBulanIniJumlahHarga = $sdBulanLaluJumlahHarga + $bulanIniJumlahHarga;
                                    $sdBulanIniBobot = $sdBulanLaluBobot + $bulanIniBobot;

                                    // Tambahkan ke total
                                    $totalSdBulanLaluVolume += $sdBulanLaluVolume;
                                    $totalSdBulanLaluJumlahHarga += $sdBulanLaluJumlahHarga;
                                    $totalSdBulanLaluBobot += $sdBulanLaluBobot;
                                    $totalBulanIniVolume += $bulanIniVolume;
                                    $totalBulanIniJumlahHarga += $bulanIniJumlahHarga;
                                    $totalBulanIniBobot += $bulanIniBobot;
                                    $totalSdBulanIniVolume += $sdBulanIniVolume;
                                    $totalSdBulanIniJumlahHarga += $sdBulanIniJumlahHarga;
                                    $totalSdBulanIniBobot += $sdBulanIniBobot;
                                @endphp

                                    <tr data-uraian="{{ $item->uraian_item }}" data-satuan="{{ $satuan }}" data-item-id="{{ $item->id }}"
                                        class="text-gray-800 dark:text-gray-200">
                                    <td class="border py-2 px-4 text-center whitespace-nowrap overflow-hidden truncate max-w-[40px]">{{ $itemIndex + 1 }}</td>
                                    <td class="border py-2 px-4 pl-8 whitespace-nowrap overflow-hidden truncate max-w-[180px]">{{ $item->uraian_item }}</td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($volume, 2) }}</td>
                                    <td class="border py-2 px-4 text-center whitespace-nowrap overflow-hidden truncate max-w-[70px]">{{ $satuan }}</td>
                                    <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                        <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                            <span class="float-left mr-1">Rp.</span>
                                            <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($hargaSatuan, 2, ',', '.') }}</span>
                                        </span>
                                    </td>
                                    <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                        <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                            <span class="float-left mr-1">Rp.</span>
                                            <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($jumlahHarga, 2, ',', '.') }}</span>
                                        </span>
                                    </td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($itemBobot, 2) }}%</td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($sdBulanLaluVolume, 2) }}</td>
                                    <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                        <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                            <span class="float-left mr-1">Rp.</span>
                                            <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($sdBulanLaluJumlahHarga, 2, ',', '.') }}</span>
                                        </span>
                                    </td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($sdBulanLaluBobot, 2) }}%</td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($bulanIniVolume, 2) }}</td>
                                    <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                        <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                            <span class="float-left mr-1">Rp.</span>
                                            <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($bulanIniJumlahHarga, 2, ',', '.') }}</span>
                                        </span>
                                    </td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($bulanIniBobot, 2) }}%</td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($sdBulanIniVolume, 2) }}</td>
                                    <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                        <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                            <span class="float-left mr-1">Rp.</span>
                                            <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($sdBulanIniJumlahHarga, 2, ',', '.') }}</span>
                                        </span>
                                    </td>
                                    <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($sdBulanIniBobot, 2) }}%</td>
                                </tr>
                            @endforeach
                        @endforeach

                        <!-- Total Row -->
                        <tr class="bg-blue-200 dark:bg-blue-800 font-bold text-gray-800 dark:text-gray-200">
                            <td class="border py-2 px-4 text-center whitespace-nowrap overflow-hidden truncate" colspan="5">TOTAL</td>
                            <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                    <span class="float-left mr-1">Rp.</span>
                                    <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($totalJumlahHarga, 2, ',', '.') }}</span>
                                </span>
                            </td>
                            <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($totalRABBobot, 2) }}%</td>

                                <!-- s/d Minggu/Bulan lalu Total -->
                                <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($totalSdBulanLaluVolume, 2) }}
                                </td>
                            <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                    <span class="float-left mr-1">Rp.</span>
                                    <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($totalSdBulanLaluJumlahHarga, 2, ',', '.') }}</span>
                                </span>
                            </td>
                                <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($totalSdBulanLaluBobot, 2) }}%
                                </td>

                                <!-- Minggu/Bulan ini Total -->
                            <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($totalBulanIniVolume, 2) }}</td>
                            <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                    <span class="float-left mr-1">Rp.</span>
                                    <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($totalBulanIniJumlahHarga, 2, ',', '.') }}</span>
                                </span>
                            </td>
                            <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($totalBulanIniBobot, 2) }}%</td>

                                <!-- s/d Minggu/Bulan ini Total -->
                                <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($totalSdBulanIniVolume, 2) }}
                                </td>
                            <td class="border py-2 px-4 whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[120px]">
                                    <span class="float-left mr-1">Rp.</span>
                                    <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[90px]">{{ number_format($totalSdBulanIniJumlahHarga, 2, ',', '.') }}</span>
                                </span>
                            </td>
                                <td class="border py-2 px-4 text-right whitespace-nowrap overflow-hidden truncate max-w-[80px]">{{ number_format($totalSdBulanIniBobot, 2) }}%
                                </td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Ringkasan Progres -->
            <div>
            <table class="min-w-full bg-blue-200 dark:bg-blue-800 border text-gray-800 dark:text-gray-200">
                <tbody>
                    <tr>
                        <td class="py-2 px-4 border font-bold text-right whitespace-nowrap overflow-hidden truncate">Total Nilai Kontrak</td>
                        <td class="py-2 px-4 border font-bold text-left whitespace-nowrap overflow-hidden truncate max-w-[200px]">
                            <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[200px]">
                                <span class="float-left mr-1">Rp.</span>
                                <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[170px]">{{ number_format($totalJumlahHarga, 2, ',', '.') }}</span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                            <td class="py-2 px-4 border font-bold text-right whitespace-nowrap overflow-hidden truncate">Total Realisasi Sampai
                                {{ $skala == 'minggu' ? 'Minggu' : 'Bulan' }} Ini</td>
                        <td class="py-2 px-4 border font-bold text-left whitespace-nowrap overflow-hidden truncate max-w-[200px]">
                            <span class="flex items-center whitespace-nowrap overflow-hidden truncate max-w-[200px]">
                                <span class="float-left mr-1">Rp.</span>
                                <span class="float-right whitespace-nowrap overflow-hidden truncate max-w-[170px]">{{ number_format($totalSdBulanIniJumlahHarga, 2, ',', '.') }}</span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4 border font-bold text-right whitespace-nowrap overflow-hidden truncate">Persentase Progres</td>
                        <td class="py-2 px-4 border font-bold text-left whitespace-nowrap overflow-hidden truncate max-w-[200px]">
                            {{ $totalJumlahHarga > 0 ? number_format(($totalSdBulanIniJumlahHarga / $totalJumlahHarga) * 100, 2) : 0 }}%
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        </form>

        <!-- Fixed Pagination -->
        @if (count($periodes) > 0)
            <nav role="navigation" aria-label="Pagination Navigation" class="fixed-pagination"
                id="pagination-container">
                <div class="pagination-content">
                    <div class="pagination-info hidden sm:flex items-center">
                        <span class="font-medium">{{ $skala == 'minggu' ? 'Minggu' : 'Bulan' }}
                            {{ $currentPeriod }}</span>
                        dari <span class="font-medium ml-1">{{ count($periodes) }}</span>

                        @if ($activePeriod)
                            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                                ({{ \Carbon\Carbon::parse($activePeriod['tanggal_mulai'])->format('d/m/Y') }} -
                                {{ \Carbon\Carbon::parse($activePeriod['tanggal_selesai'])->format('d/m/Y') }})
                            </span>
                        @endif

                        <!-- Skala Switch -->
                        <div class="flex items-center ml-4">
                            <form id="formSkala" action="{{ route('progres.index', ['project' => $project->id]) }}"
                                method="GET" class="flex items-center space-x-2">
                                <input type="hidden" name="periode" value="{{ $currentPeriod }}">
                                <label class="inline-flex items-center cursor-pointer">
                                    <input type="radio" name="skala" value="minggu" class="sr-only"
                                        {{ $skala == 'minggu' ? 'checked' : '' }}
                                        onclick="showLoading(); document.getElementById('formSkala').submit();">
                                    <span
                                        class="px-2 py-1 text-xs rounded-md {{ $skala == 'minggu' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300' }}">Minggu</span>
                                </label>
                                <label class="inline-flex items-center cursor-pointer">
                                    <input type="radio" name="skala" value="bulan" class="sr-only"
                                        {{ $skala == 'bulan' ? 'checked' : '' }}
                                        onclick="showLoading(); document.getElementById('formSkala').submit();">
                                    <span
                                        class="px-2 py-1 text-xs rounded-md {{ $skala == 'bulan' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300' }}">Bulan</span>
                                </label>
    </form>
                        </div>
</div>

                    <div class="pagination-links">
                        {{-- Previous Page Link --}}
                        @if ($currentPeriod <= 1)
                            <span class="pagination-arrow-disabled" aria-disabled="true">
                                <i class="fas fa-chevron-left text-xs"></i>
                            </span>
                        @else
                            <a href="{{ route('progres.index', ['project' => $project->id, 'periode' => $currentPeriod - 1, 'skala' => $skala]) }}"
                                class="pagination-arrow" rel="prev">
                                <i class="fas fa-chevron-left text-xs"></i>
                            </a>
                        @endif

                        <!-- Skala Switch untuk Mobile -->
                        <div class="sm:hidden flex items-center mx-2">
                            <form id="formSkalaMobile" action="{{ route('progres.index', ['project' => $project->id]) }}"
                                method="GET" class="flex items-center gap-1">
                                <input type="hidden" name="periode" value="{{ $currentPeriod }}">
                                <label class="inline-flex items-center cursor-pointer">
                                    <input type="radio" name="skala" value="minggu" class="sr-only"
                                        {{ $skala == 'minggu' ? 'checked' : '' }}
                                        onclick="showLoading(); document.getElementById('formSkalaMobile').submit();">
                                    <span
                                        class="px-1.5 py-1 text-xs rounded-md {{ $skala == 'minggu' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300' }}">M</span>
                                </label>
                                <label class="inline-flex items-center cursor-pointer">
                                    <input type="radio" name="skala" value="bulan" class="sr-only"
                                        {{ $skala == 'bulan' ? 'checked' : '' }}
                                        onclick="showLoading(); document.getElementById('formSkalaMobile').submit();">
                                    <span
                                        class="px-1.5 py-1 text-xs rounded-md {{ $skala == 'bulan' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300' }}">B</span>
                                </label>
                            </form>

                            @if ($activePeriod)
                                <div
                                    class="ml-1 text-xs text-gray-500 dark:text-gray-400 max-w-[80px] overflow-hidden whitespace-nowrap">
                                    {{ \Carbon\Carbon::parse($activePeriod['tanggal_mulai'])->format('d/m') }}-{{ \Carbon\Carbon::parse($activePeriod['tanggal_selesai'])->format('d/m') }}
                                </div>
                            @endif

                            <span
                                class="ml-1 text-xs bg-blue-500 text-white px-1.5 py-0.5 rounded-full">{{ $currentPeriod }}/{{ count($periodes) }}</span>
                        </div>

                        {{-- First Page Link --}}
                        @if ($currentPeriod > 3)
                            <a href="{{ route('progres.index', ['project' => $project->id, 'periode' => 1, 'skala' => $skala]) }}"
                                class="pagination-link">
                                1
                            </a>

                            @if ($currentPeriod > 4)
                                <span class="pagination-ellipsis">...</span>
                            @endif
                        @endif

                        {{-- Pagination Elements --}}
                        @for ($i = max(1, $currentPeriod - 2); $i <= min(count($periodes), $currentPeriod + 2); $i++)
                            @if ($i == $currentPeriod)
                                <span class="pagination-current">
                                    {{ $i }}
                                </span>
                            @else
                                <a href="{{ route('progres.index', ['project' => $project->id, 'periode' => $i, 'skala' => $skala]) }}"
                                    class="pagination-link">
                                    {{ $i }}
                                </a>
                            @endif
                        @endfor

                        {{-- Last Page Link --}}
                        @if ($currentPeriod < count($periodes) - 2)
                            @if ($currentPeriod < count($periodes) - 3)
                                <span class="pagination-ellipsis">...</span>
                            @endif

                            <a href="{{ route('progres.index', ['project' => $project->id, 'periode' => count($periodes), 'skala' => $skala]) }}"
                                class="pagination-link">
                                {{ count($periodes) }}
                            </a>
                        @endif

                        {{-- Next Page Link --}}
                        @if ($currentPeriod < count($periodes))
                            <a href="{{ route('progres.index', ['project' => $project->id, 'periode' => $currentPeriod + 1, 'skala' => $skala]) }}"
                                class="pagination-arrow" rel="next">
                                <i class="fas fa-chevron-right text-xs"></i>
                            </a>
                        @else
                            <span class="pagination-arrow-disabled" aria-disabled="true">
                                <i class="fas fa-chevron-right text-xs"></i>
                            </span>
                        @endif
                    </div>
                </div>
            </nav>
        @endif
    </div>

    {{-- Tooltip Element --}}
    <div id="customCellTooltip" class="hidden fixed z-[9999] px-3 py-2 text-sm font-medium text-white rounded-lg shadow-xl whitespace-normal break-words max-w-xs" role="tooltip">
    </div>

    @if (session('success'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.showSuccessToast("{{ session('success') }}");
    });
</script>
@endif

    @if (session('error'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.showErrorToast("{{ session('error') }}");
    });
</script>
@endif

    @if (session('info'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.showInfoToast("{{ session('info') }}");
    });
</script>
@endif

    <!-- Modal Daftar Volume -->
    <div id="modalDaftarVolume"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 overflow-y-auto py-8 text-sm hidden">
        <div
            class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-4xl overflow-hidden flex flex-col relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
            <div
                class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                <div class="flex justify-between items-center">
                    <h1 class="text-white dark:text-dark-text text-lg font-semibold">
                        Daftar Volume Progres - Minggu ke <span id="periodeLabel"></span>
                    </h1>
                    <button type="button"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-gray-300 rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none"
                        onclick="closeModal('modalDaftarVolume')">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text">
                <div class="flex items-center justify-between mb-4 border-b dark:border-dark-border pb-4">
                    <div class="relative flex-1 max-w-md">
                        <input type="text" id="searchDaftarVolume" placeholder="Cari uraian pekerjaan..."
                            class="w-full pl-10 pr-4 py-2 border rounded-lg bg-white dark:bg-dark-card text-gray-800 dark:text-gray-200"
                            oninput="filterDaftarVolumeTable()">
                        <div class="absolute left-3 top-2.5 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>

                    <button type="button" id="btnTambahVolume"
                        class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg flex items-center whitespace-nowrap shadow-sm hover:shadow transform hover:scale-105 transition-all duration-150">
                        <i class="fas fa-plus-circle mr-2"></i> Tambah Volume
                    </button>
                </div>

                <div class="overflow-visible" style="overflow-x: visible;">
                    <table class="min-w-full bg-white dark:bg-dark-card border text-sm">
                        <thead
                            class="bg-light-accent/20 dark:bg-dark-accent/30 sticky top-0 z-10 text-gray-800 dark:text-gray-200">
                            <tr>
                                <th class="border py-2 px-4 w-[50px] text-center">No</th>
                                <th class="border py-2 px-4">Uraian Pekerjaan</th>
                                <th class="border py-2 px-4 w-[100px] text-center">Volume</th>
                                <th class="border py-2 px-4 w-[80px] text-center">Satuan</th>
                                <th class="border py-2 px-4 w-[60px] text-center relative">Aksi</th>
                            </tr>
                        </thead>
                        <tbody id="daftarVolumeTableBody">
                            <!-- Data akan diisi melalui JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-center">
                <button type="button"
                    class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg flex items-center whitespace-nowrap shadow-sm hover:shadow transform hover:scale-105 transition-all duration-150"
                    onclick="closeModal('modalDaftarVolume')">
                    <i class="fas fa-check-circle mr-2"></i> Selesai
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Input Volume Progres -->
    <div id="modalInputVolume"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 overflow-y-auto py-8 text-sm hidden">
        <div
            class="bg-white dark:bg-dark-card p-0 rounded-xl shadow-lg w-full max-w-lg overflow-hidden flex flex-col relative mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
            <div
                class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10">
                <div class="flex justify-between items-center">
                    <h1 class="text-white dark:text-dark-text text-lg font-semibold">
                        Input Volume Progres - Minggu ke <span id="periodeInputLabel"></span>
                    </h1>
                    <button type="button"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-gray-300 rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none"
                        onclick="closeModal('modalInputVolume')">
                        <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <div class="p-6 bg-white dark:bg-dark-card text-light-text dark:text-dark-text overflow-y-auto"
                style="scrollbar-width: thin;">
                <form id="formInputVolume">
                    <input type="hidden" id="projectId" name="project_id" value="{{ $project->id }}">
                    <input type="hidden" id="currentPeriod" name="current_period" value="{{ $currentPeriod }}">

                    <div class="mb-4">
                        <label for="itemPekerjaanId"
                            class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Item Pekerjaan</label>
                        <select id="itemPekerjaanId" name="item_pekerjaan_id"
                            class="w-full p-2 border rounded-lg bg-white dark:bg-dark-card text-gray-800 dark:text-gray-200"
                            onchange="updateItemDetails()">
                            <option value="">Pilih Item Pekerjaan</option>
                            <!-- Options akan diisi melalui JavaScript -->
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="volumeRealisasi"
                            class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                            Masukkan Volume <span id="sisaVolumeLabel"
                                class="text-light-accent dark:text-dark-accent">(Sisa Volume:
                                <span id="sisaVolumeValue">0</span>)</span>
                        </label>
                        <input type="number" id="volumeRealisasi" name="volume_realisasi" step="0.01"
                            min="0"
                            class="w-full p-2 border rounded-lg bg-white dark:bg-dark-card text-gray-800 dark:text-gray-200"
                            oninput="calculateValues()">
                    </div>

                    <div class="mb-4">
                        <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Jumlah Harga</label>
                        <div class="p-2 border rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 flex justify-between items-center">
                            <span class="text-sm">Rp.</span>
                            <span id="jumlahHargaValue" class="font-medium">0</span>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Bobot s/d Minggu ini</label>
                        <div class="p-2 border rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 flex justify-between items-center">
                            <span class="text-sm">Bobot:</span>
                            <div class="flex items-center">
                                <span id="bobotValue" class="font-medium mr-1">0.0000</span>
                                <span class="text-sm">%</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Sisa Bobot</label>
                        <div class="p-2 border rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 flex justify-between items-center">
                            <span class="text-sm">Sisa:</span>
                            <div class="flex items-center">
                                <span id="sisaBobotValue" class="font-medium mr-1">0.0000</span>
                                <span class="text-sm">%</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                <button type="button"
                    class="bg-gray-500 hover:bg-gray-700 text-white dark:text-dark-text px-4 py-2 rounded-lg flex items-center whitespace-nowrap transition-colors duration-150"
                    onclick="closeModal('modalInputVolume')">
                    <i class="fas fa-times-circle mr-2"></i> Batal
                </button>
                <button type="button" id="btnSimpanProgres"
                    class="bg-light-accent hover:bg-light-accent/80 dark:bg-dark-accent dark:hover:bg-dark-accent/80 text-white dark:text-dark-text px-4 py-2 rounded-lg flex items-center whitespace-nowrap shadow-sm hover:shadow transform hover:scale-105 transition-all duration-150">
                    <i class="fas fa-save mr-2"></i> Simpan Progres
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Konfirmasi Hapus -->
    <div id="modalKonfirmasiHapus" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden">
        <div class="confirm-box bg-white dark:bg-dark-card rounded-lg shadow-lg p-6 max-w-sm mx-auto">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-5">Konfirmasi</h3>
                <p class="confirm-message text-gray-700 dark:text-gray-300 mb-6">Apakah Anda yakin ingin menghapus data progres ini?</p>
                <div class="flex justify-center space-x-4">
                    <button type="button" id="btnBatalHapus" class="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        Tidak
                    </button>
                    <button type="button" id="btnKonfirmasiHapus" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        Ya, Hapus
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        // Configuration for progres page
        window.progresConfig = {
            projectId: {{ $project->id }},
            currentPeriod: {{ $currentPeriod }},
            totalJumlahHarga: {{ $totalJumlahHarga ?? 0 }}
        };
    </script>
    @vite(['resources/js/progres.js'])
@endsection
