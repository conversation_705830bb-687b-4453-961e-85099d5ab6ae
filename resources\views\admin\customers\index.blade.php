@extends('layouts.app')

@section('content')
    <div class="container mx-auto max-w-[1080px] p-6">
        <h1 class="text-2xl font-bold mb-6 text-light-text dark:text-dark-text">Manajemen Customer</h1>

        <div class="flex justify-between items-center mb-4">
            <button onclick="openCustomerModal(false)"
                class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                <i class="fas fa-plus-circle"></i> <PERSON>bah Customer
            </button>
            <div class="flex items-center space-x-2">
                <form id="searchForm" action="{{ route('customers.index') }}" method="GET"
                    class="flex items-center space-x-2">
                    <label for="searchCustomer" class="text-sm font-medium text-light-text dark:text-dark-text">Cari
                        Data:</label>
                    <input type="text" id="searchCustomer" name="search" placeholder="Cari..."
                        class="border px-3 py-2 rounded dark:bg-dark-bg-secondary dark:border-dark-border dark:text-dark-text"
                        value="{{ request('search') }}">
                    <button type="submit"
                        class="bg-light-accent hover:bg-light-accent/80 transition-colors text-white px-3 py-2 rounded">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>

        <!-- Tabel Data Customer -->
        <div class="overflow-y-auto max-h-[65vh]">
            <table id="customerTable" class="text-sm min-w-full bg-white border dark:bg-dark-card dark:border-dark-border">
                <thead class="bg-green-200 sticky top-0 z-10 dark:bg-dark-table-header">
                    <tr>
                        <th class="py-2 px-4 border dark:border-dark-border">No.</th>
                        <th class="py-2 px-4 border dark:border-dark-border">Nama</th>
                        <th class="py-2 px-4 border dark:border-dark-border">Email</th>
                        <th class="py-2 px-4 border dark:border-dark-border">Jumlah Proyek</th>
                        <th class="py-2 px-4 border dark:border-dark-border">Tanggal Daftar</th>
                        <th class="py-2 px-4 border dark:border-dark-border">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($customers as $index => $customer)
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-hover">
                            <td class="py-2 px-4 border dark:border-dark-border text-center">
                                {{ ($customers->currentPage() - 1) * $customers->perPage() + $index + 1 }}
                            </td>
                            <td class="py-2 px-4 border dark:border-dark-border">
                                {{ $customer->name }}
                            </td>
                            <td class="py-2 px-4 border dark:border-dark-border">
                                {{ $customer->email }}
                            </td>
                            <td class="py-2 px-4 border dark:border-dark-border text-center">
                                {{ $projectCounts[$customer->id] ?? 0 }}
                            </td>
                            <td class="py-2 px-4 border dark:border-dark-border">
                                {{ $customer->created_at->format('d M Y') }}
                            </td>
                            <td class="py-2 px-4 border dark:border-dark-border text-center">
                                <!-- Tombol Action untuk Context Menu -->
                                <button type="button"
                                    class="action-btn-item py-2 px-4 text-blue-500 hover:text-blue-700 p-1 rounded-full hover:bg-blue-200 transition-colors duration-150"
                                    data-customer-id="{{ $customer->id }}" data-customer-name="{{ $customer->name }}"
                                    data-customer-email="{{ $customer->email }}"
                                    data-customer-address="{{ $customer->address ?? '' }}"
                                    data-customer-phone="{{ $customer->phone ?? '' }}"
                                    data-customer-view-url="{{ route('customers.show', $customer->id) }}"
                                    onclick="handleCustomerContextMenu(event, this)">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="py-2 px-4 border dark:border-dark-border text-center">
                                Tidak ada data customer
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Add Pagination Links -->
        {{ $customers->links('components.user-fixed-pagination') }}
    </div>

    <!-- Modal Customer -->
    <div id="customerModal" tabindex="-1" aria-hidden="true"
        class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 hidden z-50 overflow-y-auto py-8 text-sm">
        <div class="relative bg-white dark:bg-dark-card p-0 rounded-lg shadow-lg w-full max-w-md mx-auto my-auto border-2 border-light-accent dark:border-dark-accent">
            <!-- Modal header -->
            <div class="top-0 bg-gradient-to-r from-light-accent to-light-accent/90 dark:from-dark-accent dark:to-dark-accent/90 p-4 z-10 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <h3 class="text-white dark:text-dark-text text-lg font-semibold" id="customerModalTitle">
                    Tambah Customer
                </h3>
                <button type="button" data-modal-hide="customerModal"
                        class="bg-white/20 hover:bg-white/30 dark:bg-dark-bg/30 dark:hover:bg-dark-bg/40 text-white dark:text-dark-text rounded-lg w-8 h-8 flex items-center justify-center transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 group focus:outline-none">
                    <svg class="h-5 w-5 group-hover:rotate-90 transition-transform duration-300" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
                </div>
            </div>
            <!-- Modal body -->
            <div class="p-6 overflow-y-auto" style="scrollbar-width: thin;">
                <form id="customerForm">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <input type="hidden" id="customerId" value="">
                    <input type="hidden" id="customerMethod" value="POST">

                    <div class="mb-4">
                        <label for="name"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nama</label>
                        <input type="text" id="name" name="name"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100"
                            placeholder="Masukkan nama customer" required>
                        <span id="nameError" class="text-red-500 text-xs mt-1 hidden">Nama wajib diisi</span>
                    </div>
                    <div class="mb-4">
                        <label for="email"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                        <input type="email" id="email" name="email"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100"
                            placeholder="Masukkan email customer" required>
                        <span id="emailError" class="text-red-500 text-xs mt-1 hidden">Email wajib diisi</span>
                    </div>
                    <div class="mb-4">
                        <label for="password"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Password</label>
                        <input type="password" id="password" name="password"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100"
                            placeholder="Masukkan password">
                        <span id="passwordError" class="text-red-500 text-xs mt-1 hidden">Password wajib diisi</span>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400" id="passwordHelp">Kosongkan jika
                            tidak ingin mengubah password</p>
                    </div>

                    <div class="mb-4">
                        <label for="address"
                            class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Alamat</label>
                        <textarea id="address" name="address"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100"
                            placeholder="Masukkan alamat customer" rows="2"></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="phone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nomor
                            Telepon</label>
                        <input type="text" id="phone" name="phone"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-dark-bg-secondary dark:border-gray-600 dark:placeholder-gray-400 dark:text-white hover:bg-gray-100"
                            placeholder="Masukkan nomor telepon">
                    </div>

                    <!-- Modal footer -->
                    <div class="mt-6 flex justify-end space-x-4">
                        <button data-modal-hide="customerModal" type="button"
                            class="bg-gray-500 hover:bg-gray-700 hover:text-blue-100 text-white px-4 py-2 rounded">
                            <i class="fas fa-times-circle"></i> Batal
                        </button>
                        <button type="button" onclick="saveCustomer()"
                            class="bg-light-accent hover:bg-light-accent/80 text-white px-4 py-2 rounded transition-all duration-200 shadow-sm hover:shadow transform hover:scale-105">
                            <i class="fas fa-save mr-1"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

<!-- Context Menu for Customer Actions -->
<div id="customer-context-menu"
    class="hidden absolute bg-white dark:bg-dark-bg-secondary shadow-lg rounded-md py-2 w-35 border border-gray-200 dark:border-dark-accent/20 z-50 text-sm">
    <button id="btn-edit-customer"
        class="w-full px-4 py-2 text-left text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/20 hover:text-blue-700 text-sm flex items-center">
        <i class="fas fa-edit mr-2"></i> Edit Customer
    </button>
    <a id="btn-view-customer" href="javascript:void(0);"
        class="w-full px-4 py-2 text-left text-green-500 hover:bg-green-100 dark:hover:bg-green-900/20 hover:text-green-700 text-sm flex items-center cursor-pointer">
        <i class="fas fa-eye mr-2"></i> Lihat Detail
    </a>
    <button id="btn-delete-customer"
        class="w-full px-4 py-2 text-left hover:bg-red-100 dark:hover:bg-red-900/20 text-sm flex items-center hover:text-red-700 text-red-500">
        <i class="fas fa-trash-alt mr-2"></i> Hapus
    </button>
</div>

@section('scripts')
    <script>
        window.csrfToken = '{{ csrf_token() }}';
    </script>
    @vite(['resources/js/customer.js', 'resources/js/table-sort.js'])
@endsection
