@props([
    'href' => '#',
    'title' => '',
    'icon' => '',
    'feature' => null,
    'description' => '',
    'showTooltip' => true,
    'class' => 'flex items-center gap-3 p-3 text-white rounded-xl transition-all hover:bg-white/10 dark:hover:bg-dark-accent/20',
    'activeClass' => 'bg-white/20 dark:bg-dark-accent/30',
    'inactiveClass' => 'opacity-50 cursor-not-allowed bg-gray-500/20 dark:bg-gray-600/20'
])

@php
    $hasAccess = $feature ? hasFeatureAccess($feature) : true;
    $finalClass = $class;

    if ($hasAccess) {
        // Check if current route matches href for active state
        if (request()->url() === url($href) || request()->fullUrl() === url($href)) {
            $finalClass .= ' ' . $activeClass;
        }
    } else {
        $finalClass .= ' ' . $inactiveClass;
    }
@endphp

@if($hasAccess)
    <a href="{{ $href }}" class="{{ $finalClass }}" @if($showTooltip && $description) title="{{ $description }}" @endif>
        @if($icon)
            <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg">
                <i class="{{ $icon }} text-lg"></i>
            </div>
        @endif
        <div class="flex-1">
            <span class="font-medium">{{ $title }}</span>
            {{ $slot }}
        </div>
    </a>
@else
    <div class="{{ $finalClass }}"
         data-feature="{{ $feature }}"
         @if($showTooltip)
             title="Fitur ini tidak tersedia dalam paket langganan Anda. Silakan upgrade paket untuk mengakses fitur ini."
         @endif
         onclick="event.preventDefault(); if(typeof window.subscriptionChecker !== 'undefined') { window.subscriptionChecker.showFeatureRestrictedToast({{ json_encode($feature) }}); }">
        @if($icon)
            <div class="p-2 bg-white/10 dark:bg-dark-accent/20 rounded-lg opacity-50">
                <i class="{{ $icon }} text-lg"></i>
            </div>
        @endif
        <div class="flex-1">
            <span class="font-medium">{{ $title }}</span>
            {{ $slot }}
        </div>
        <i class="fas fa-lock text-gray-400 ml-auto"></i>
    </div>
@endif
