<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\UpahController;
use App\Http\Controllers\BahanController;
use App\Http\Controllers\AlatController;
use App\Http\Controllers\RabController;
use App\Http\Controllers\VolumeController;
use App\Http\Controllers\AhsController;
use App\Http\Controllers\KategoriPekerjaanController;
use App\Http\Controllers\ItemPekerjaanController;
use App\Http\Controllers\VolumeCalculationController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\TimeScheduleController;
use App\Http\Controllers\Auth\GoogleController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ProfileController;
use App\Http\Requests\EmailVerificationRequest;
use App\Http\Controllers\ProgresController;

// Public routes (no authentication required)
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);

    // Password Reset Routes
    Route::get('/forgot-password', [AuthController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetForm'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'reset'])->name('password.update');

    // Social Authentication routes
    Route::get('/auth/google', [GoogleController::class, 'redirectToGoogle'])->name('auth.google');
    Route::get('/auth/google/callback', [GoogleController::class, 'handleGoogleCallback']);
});

// Email Verification Routes
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', [AuthController::class, 'showVerificationNotice'])
        ->name('verification.notice');
    Route::post('/email/verification-notification', [AuthController::class, 'resendVerificationEmail'])
        ->middleware(['throttle:6,1'])->name('verification.send');
});

// Rute verifikasi email - tidak perlu login
Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verifyEmail'])
    ->middleware(['signed', 'throttle:6,1'])
    ->name('verification.verify');

// Rute untuk meminta verifikasi email baru - tidak perlu login
Route::get('/email/need-verification', [AuthController::class, 'needVerification'])
    ->name('verification.need');
Route::post('/email/need-verification', [AuthController::class, 'sendVerificationLinkByEmail'])
    ->middleware(['throttle:6,1'])
    ->name('verification.need.send');

// Root route - always redirect to welcome page
Route::get('/', function () {
    return redirect()->route('visitor.welcome');
});

// Protected routes (authentication required)
Route::middleware('auth')->group(function () {
    // AHS routes - basic access for all authenticated users
    Route::get('/ahs', [AhsController::class, 'index'])->name('ahs.index');
    Route::get('ahs/{id}/detail', [AhsController::class, 'getDetail'])->name('ahs.detail.get');
    Route::get('/ahsp/all', [AhsController::class, 'all'])->name('ahsp.all');

    // AHSP routes - conditional subscription check in controller based on source
    Route::resource('ahs', AhsController::class)->except(['create', 'edit', 'show', 'index']);
    Route::post('ahs/detail', [AhsController::class, 'storeDetail'])->name('ahs.detail.store');
    Route::put('ahs/{id}/detail', [AhsController::class, 'updateDetail'])->name('ahs.detail.update');
    Route::put('ahs/{id}/detail1', [AhsController::class, 'updateDetail1'])->name('ahs.detail.update1');

    // Download routes for PDF and Excel with feature-specific restrictions
    Route::middleware(['auth'])->group(function () {
        // PDF Export routes
        Route::middleware([\App\Http\Middleware\CheckSubscription::class . ':Export ke PDF'])->group(function () {
            Route::get('/download/rab-pdf', [\App\Http\Controllers\PdfController::class, 'downloadRabPdf'])->name('download.rab-pdf');
        });

        // Excel Export routes
        Route::middleware([\App\Http\Middleware\CheckSubscription::class . ':Export ke Excel'])->group(function () {
            Route::get('/download/rab-excel', [\App\Http\Controllers\ExcelController::class, 'downloadRabExcel'])->name('download.rab-excel');
        });

        // Excel with Formula Export routes
        Route::middleware([\App\Http\Middleware\CheckSubscription::class . ':Export ke Excel dan PDF'])->group(function () {
            Route::get('/download/rab-excel-formula', [\App\Http\Controllers\ExcelController::class, 'downloadRabExcelFormula'])->name('download.rab-excel-formula');
        });
    });

    // Rute khusus customer (proyek dan RAB) - memerlukan verifikasi email
    Route::middleware([
        'auth',
        'verified',
        \App\Http\Middleware\CheckRole::class . ':customer',
        \App\Http\Middleware\CheckSubscription::class,
        \App\Http\Middleware\CheckProjectLimit::class
    ])->group(function () {
        // Project routes
        Route::get('/projects/{project}', [ProjectController::class, 'show'])->name('projects.show');
        Route::post('/projects', [ProjectController::class, 'store'])->name('projects.store');
        Route::put('/projects/{project}', [ProjectController::class, 'update'])->name('projects.update');
        Route::delete('/projects/{project}', [ProjectController::class, 'destroy'])->name('projects.destroy');

        // Project Share routes
        Route::get('/projects/{project}/share', [ProjectController::class, 'showShareForm'])->name('projects.share');
        Route::post('/projects/{project}/share', [ProjectController::class, 'share'])->name('projects.share.store');
        Route::delete('/projects/{project}/share/{shareId}', [ProjectController::class, 'removeShare'])->name('projects.share.remove');
        Route::put('/projects/{project}/share/{shareId}', [ProjectController::class, 'updateShareRole'])->name('projects.share.update');
        Route::delete('/projects/{projectId}/remove-own-access', [ProjectController::class, 'removeOwnAccess'])->name('projects.remove-own-access');
        Route::post('/projects/{project}/duplicate', [ProjectController::class, 'duplicate'])->name('projects.duplicate');
        Route::post('/projects/{project}/update-ppn', [ProjectController::class, 'updatePPN'])->name('projects.update-ppn');

        // RAB routes
        Route::resource('rab', RabController::class)->except(['create', 'edit', 'show', 'index']);
        Route::post('/rab', [RabController::class, 'index'])->name('rab.index');
        Route::get('/rab', [RabController::class, 'index']);
        Route::get('/rab/preview-pdf', [\App\Http\Controllers\PdfController::class, 'previewRabPdf'])->name('rab.preview-pdf');
        Route::get('/rab/kategori-data', [RabController::class, 'getKategoriData'])->name('rab.kategori-data');
        Route::post('/rab/update-totals', [RabController::class, 'updateTotals'])->name('rab.update-totals');

        // Time Schedule routes with feature restriction
        Route::middleware([\App\Http\Middleware\CheckSubscription::class . ':Time Schedule'])->group(function () {
            Route::get('/time-schedule/project-data', [TimeScheduleController::class, 'getProjectSchedule'])->name('time-schedule.project-data');
            Route::get('/time-schedule/{project?}', [TimeScheduleController::class, 'index'])->name('time-schedule.index');
            Route::post('/time-schedule/index', [TimeScheduleController::class, 'index'])->name('time-schedule.post-index');
            Route::post('/time-schedule/{id}/progress', [TimeScheduleController::class, 'updateProgress'])->name('time-schedule.update-progress');
        });

        // Volume routes
        Route::resource('volume', VolumeController::class)->except(['create', 'edit', 'show']);
        Route::post('/volume/store-custom', [VolumeController::class, 'store'])->name('volume.store-custom');

        // Kategori Pekerjaan routes
        Route::post('/kategori-pekerjaan', [KategoriPekerjaanController::class, 'store'])->name('kategori-pekerjaan.store');
        Route::get('/kategori-pekerjaan/all', [KategoriPekerjaanController::class, 'all'])->name('kategori-pekerjaan.all');
        Route::get('/kategori-pekerjaan/with-items', [KategoriPekerjaanController::class, 'withItems'])->name('kategori-pekerjaan.with-items');
        Route::put('/kategori-pekerjaan/{id}', [KategoriPekerjaanController::class, 'update'])->name('kategori-pekerjaan.update');
        Route::delete('/kategori-pekerjaan/{id}', [KategoriPekerjaanController::class, 'destroy'])->name('kategori-pekerjaan.destroy');
        Route::post('/kategori-pekerjaan/update-order', [KategoriPekerjaanController::class, 'updateOrder'])->name('kategori-pekerjaan.update-order');
        Route::put('/kategori-pekerjaan/update-order', [KategoriPekerjaanController::class, 'updateOrder'])->name('kategori-pekerjaan.update-order-put');

        // Item Pekerjaan routes
        Route::post('/item-pekerjaan', [ItemPekerjaanController::class, 'store'])->name('item-pekerjaan.store');
        Route::delete('/item-pekerjaan/{id}', [ItemPekerjaanController::class, 'destroy'])->name('item-pekerjaan.destroy');
        Route::put('/item-pekerjaans/{id}/update-volume', [ItemPekerjaanController::class, 'updateVolume'])->name('item-pekerjaan.update-volume');
        Route::post('/item-pekerjaan/{id}/update-uraian', [ItemPekerjaanController::class, 'updateUraian'])->name('item-pekerjaan.update-uraian');
        Route::get('/item-pekerjaans/{id}', [ItemPekerjaanController::class, 'show'])->name('item-pekerjaan.show');
        Route::get('/item-pekerjaan/harga-satuan', [ItemPekerjaanController::class, 'getHargaSatuan'])->name('item-pekerjaan.harga-satuan');

        // Volume Calculations routes
        Route::get('/volume-calculations', [VolumeCalculationController::class, 'index'])->name('volume-calculations.index');
        Route::post('/volume-calculations', [VolumeCalculationController::class, 'store'])->name('volume-calculations.store');
        Route::get('/volume-calculations/{id}', [VolumeCalculationController::class, 'show']);
        Route::delete('/volume-calculations/{id}', [VolumeCalculationController::class, 'destroy']);
        Route::put('/volume-calculations/{id}', [VolumeCalculationController::class, 'update']);

        // AHSP data sudah dipindahkan ke area yang dapat diakses semua user
        // Route::get('/ahsp/all', [AhsController::class, 'all'])->name('ahsp.all');

        // AHS Detail routes yang dibutuhkan oleh RAB sudah dipindahkan ke area yang dapat diakses semua user
        // Route::get('ahs/{id}/detail', [AhsController::class, 'getDetail'])->name('ahs.detail.get');

        // Akses halaman AHS sudah dipindahkan ke area yang dapat diakses semua user
        // Route::get('/ahs', [AhsController::class, 'index'])->name('ahs.index');
        // Route::resource('ahs', AhsController::class)->except(['create', 'edit', 'show']);

        // AHS Detail routes lengkap sudah dipindahkan ke area yang dapat diakses semua user
        // Route::post('ahs/detail', [AhsController::class, 'storeDetail'])->name('ahs.detail.store');
        // Route::put('ahs/{id}/detail', [AhsController::class, 'updateDetail'])->name('ahs.detail.update');
        // Route::put('ahs/{id}/detail1', [AhsController::class, 'updateDetail1'])->name('ahs.detail.update1');
    });

    // Rute khusus admin (dashboard, upah, bahan, alat)
    Route::middleware([\App\Http\Middleware\CheckRole::class . ':admin'])->group(function () {
        // Dashboard Admin
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');

        // Customer Management routes
        Route::get('/customers', [CustomerController::class, 'index'])->name('customers.index');
        Route::post('/customers', [CustomerController::class, 'store'])->name('customers.store');
        Route::get('/customers/{id}', [CustomerController::class, 'show'])->name('customers.show');
        Route::put('/customers/{id}', [CustomerController::class, 'update'])->name('customers.update');
        Route::delete('/customers/{id}', [CustomerController::class, 'destroy'])->name('customers.destroy');

        // Upah routes
        Route::resource('upah', UpahController::class)->except(['create', 'edit', 'show']);
        Route::get('/upah', [UpahController::class, 'index'])->name('upah.index');

        // Bahan routes
        Route::resource('bahan', BahanController::class)->except(['create', 'edit', 'show']);
        Route::get('/bahan', [BahanController::class, 'index'])->name('bahan.index');

        // Alat routes
        Route::resource('alat', AlatController::class)->except(['create', 'edit', 'show']);
        Route::get('/alat', [AlatController::class, 'index'])->name('alat.index');

        // Admin tidak perlu mendefinisikan rute AHS lagi karena sudah ada di bagian customer
        // dan admin memiliki role yang berbeda

        // Subscription Plan Management routes
        Route::get('/admin/subscription-plans', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'index'])->name('admin.subscription-plans.index');
        Route::get('/admin/subscription-plans/create', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'create'])->name('admin.subscription-plans.create');
        Route::post('/admin/subscription-plans', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'store'])->name('admin.subscription-plans.store');
        Route::get('/admin/subscription-plans/{id}/edit', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'edit'])->name('admin.subscription-plans.edit');
        Route::put('/admin/subscription-plans/{id}', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'update'])->name('admin.subscription-plans.update');
        Route::delete('/admin/subscription-plans/{id}', [\App\Http\Controllers\Admin\SubscriptionPlanController::class, 'destroy'])->name('admin.subscription-plans.destroy');

        // Payment Management routes
        Route::get('/admin/payments', [\App\Http\Controllers\Admin\PaymentController::class, 'index'])->name('admin.payments.index');
        Route::get('/admin/payments/{id}', [\App\Http\Controllers\Admin\PaymentController::class, 'show'])->name('admin.payments.show');
        Route::post('/admin/payments/{id}/verify', [\App\Http\Controllers\Admin\PaymentController::class, 'verify'])->name('admin.payments.verify');
        Route::post('/admin/payments/{id}/reject', [\App\Http\Controllers\Admin\PaymentController::class, 'reject'])->name('admin.payments.reject');

        // System Settings routes
        Route::get('/admin/settings', [\App\Http\Controllers\Admin\SystemSettingController::class, 'index'])->name('admin.settings.index');
        Route::post('/admin/settings', [\App\Http\Controllers\Admin\SystemSettingController::class, 'update'])->name('admin.settings.update');
    });

    // Rute umum (untuk semua pengguna yang sudah login)
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'getProfile'])->name('profile.get');
    Route::post('/profile', [ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::post('/profile/reset-photo', [ProfileController::class, 'resetPhoto'])->name('profile.reset-photo');

    // Rute umum untuk semua pengguna yang sudah login
    Route::get('/subscriptions/success', [\App\Http\Controllers\SubscriptionController::class, 'success'])->name('subscriptions.success');
    Route::get('/subscriptions/error', [\App\Http\Controllers\SubscriptionController::class, 'error'])->name('subscriptions.error');

    // Payment Gateway routes - digunakan oleh semua pengguna
    Route::get('/payment-gateway/config', [\App\Http\Controllers\PaymentGatewayController::class, 'getConfig'])->name('payment-gateway.config')->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);
    Route::post('/payment-gateway/xendit/invoice', [\App\Http\Controllers\PaymentGatewayController::class, 'createXenditInvoice'])->name('payment-gateway.xendit.invoice')->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);

    // Rute khusus untuk customer
    Route::middleware([\App\Http\Middleware\CustomerOnly::class])->group(function () {
        // Project index route (same access level as subscriptions)
        Route::get('/proyek', [\App\Http\Controllers\ProjectController::class, 'index'])->name('proyek');

        // Subscription routes
        Route::get('/subscriptions', [\App\Http\Controllers\SubscriptionController::class, 'index'])->name('subscriptions.index');
        Route::get('/subscriptions/show', [\App\Http\Controllers\SubscriptionController::class, 'show'])->name('subscriptions.show');
        Route::get('/subscriptions/history', [\App\Http\Controllers\SubscriptionController::class, 'history'])->name('subscriptions.history');
        Route::get('/subscriptions/checkout/{id}', [\App\Http\Controllers\SubscriptionController::class, 'checkout'])->name('subscriptions.checkout');
        Route::post('/subscriptions/purchase/{id}', [\App\Http\Controllers\SubscriptionController::class, 'purchase'])->name('subscriptions.purchase');
        Route::post('/subscriptions/cancel', [\App\Http\Controllers\SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
        Route::post('/subscriptions/start-trial', [\App\Http\Controllers\SubscriptionController::class, 'startTrial'])->name('subscriptions.start-trial');

        // Payment routes
        Route::get('/payments', [\App\Http\Controllers\PaymentController::class, 'index'])->name('payments.index');

        // Pending Payment routes - harus ditempatkan sebelum route dengan parameter {id}
        Route::get('/payments/pending', [\App\Http\Controllers\PendingPaymentController::class, 'index'])->name('payments.pending');
        Route::get('/payments/pending/{id}/resume', [\App\Http\Controllers\PendingPaymentController::class, 'resume'])->name('payments.pending.resume');
        Route::post('/payments/pending/{id}/cancel', [\App\Http\Controllers\PendingPaymentController::class, 'cancel'])->name('payments.pending.cancel');

        // Route dengan parameter {id} harus ditempatkan setelah route yang lebih spesifik
        Route::get('/payments/{id}', [\App\Http\Controllers\PaymentController::class, 'show'])->name('payments.show');
        Route::get('/payments/{id}/upload', [\App\Http\Controllers\PaymentController::class, 'uploadForm'])->name('payments.upload-form');
        Route::post('/payments/{id}/upload', [\App\Http\Controllers\PaymentController::class, 'upload'])->name('payments.upload');
        Route::get('/payments/{id}/send-link', [\App\Http\Controllers\PaymentController::class, 'sendPaymentLink'])->name('payments.send-link');
    });

    // Progres routes with feature restriction
    Route::middleware([\App\Http\Middleware\CheckSubscription::class . ':Progres'])->group(function () {
        Route::get('/progres/{project?}', [ProgresController::class, 'index'])->name('progres.index');
        Route::post('/progres/store', [ProgresController::class, 'store'])->name('progres.store');
        Route::post('/progres/update-batch', [ProgresController::class, 'updateBatch'])->name('progres.update-batch');
    });
});

// Resource data routes for AJAX
Route::get('/upah/data', [App\Http\Controllers\UpahController::class, 'getData'])->name('upah.data');
Route::get('/alat/data', [App\Http\Controllers\AlatController::class, 'getData'])->name('alat.data');
Route::get('/bahan/data', [App\Http\Controllers\BahanController::class, 'getData'])->name('bahan.data');

// Routes for clean pagination URLs
Route::post('/upah', [App\Http\Controllers\UpahController::class, 'index'])->name('upah.post');
Route::post('/bahan', [App\Http\Controllers\BahanController::class, 'index'])->name('bahan.post');
Route::post('/alat', [App\Http\Controllers\AlatController::class, 'index'])->name('alat.post');
// Route::post('/ahs', [App\Http\Controllers\AhsController::class, 'index'])->name('ahs.post'); // Commented out to avoid conflict with ahs.store
// Route::post('/customers', [App\Http\Controllers\CustomerController::class, 'index'])->name('customers.post'); // Removed to avoid route conflict with customers.store

// AHSP Item update route - with conditional middleware for Empiris source
Route::post('/ahs/{id}/update-item', [App\Http\Controllers\AhsController::class, 'updateItem'])->name('ahs.update-item');
Route::post('/ahs', [App\Http\Controllers\AhsController::class, 'store'])->name('ahs.store');

// API routes for AJAX updates - with conditional middleware for Empiris source
Route::post('/api/ahs/{id}/update', [App\Http\Controllers\Api\AhsController::class, 'updateAjax'])->name('api.ahs.update');
Route::get('/api/ahs/{id}/grand-total', [App\Http\Controllers\Api\AhsController::class, 'getGrandTotal'])->name('api.ahs.grand-total');

// API routes for Progres with feature restriction
Route::middleware(['auth', \App\Http\Middleware\CheckSubscription::class . ':Progres'])->group(function () {
    Route::get('/api/progres/list/{projectId}', [App\Http\Controllers\ProgresController::class, 'getProgresData'])->name('api.progres.list');
    Route::get('/api/progres/items/{projectId}', [App\Http\Controllers\ProgresController::class, 'getItemPekerjaan'])->name('api.progres.items');
    Route::get('/api/progres/previous/{projectId}/{itemId}', [App\Http\Controllers\ProgresController::class, 'getPreviousProgres'])->name('api.progres.previous');
});

// API routes for Subscription checking
Route::middleware('auth')->group(function () {
    Route::get('/api/subscription/check', [App\Http\Controllers\Api\SubscriptionController::class, 'checkSubscriptionStatus'])->name('api.subscription.check');
    Route::get('/api/subscription/feature/{feature}', [App\Http\Controllers\Api\SubscriptionController::class, 'checkFeatureAccess'])->name('api.subscription.feature');
    Route::get('/api/subscription/project-limit', [App\Http\Controllers\Api\SubscriptionController::class, 'checkProjectLimit'])->name('api.subscription.project-limit');
});
// Additional API routes for Progres with feature restriction
Route::middleware(['auth', \App\Http\Middleware\CheckSubscription::class . ':Progres'])->group(function () {
    Route::get('/api/progres/edit/{id}', [App\Http\Controllers\ProgresController::class, 'edit'])->name('api.progres.edit');
    Route::post('/api/progres/store', [App\Http\Controllers\ProgresController::class, 'store'])->name('api.progres.store');
    Route::post('/api/progres/delete/{id}', [App\Http\Controllers\ProgresController::class, 'destroy'])->name('api.progres.delete');
    Route::delete('/api/progres/delete/{id}', [App\Http\Controllers\ProgresController::class, 'destroy'])->name('api.progres.delete.delete');
});

// Resource creation routes for all authenticated users (including customers)
Route::middleware('auth')->group(function () {
    Route::post('/upah', [App\Http\Controllers\UpahController::class, 'store'])->name('upah.store');
    Route::post('/alat', [App\Http\Controllers\AlatController::class, 'store'])->name('alat.store');
    Route::post('/bahan', [App\Http\Controllers\BahanController::class, 'store'])->name('bahan.store');

    // Time Schedule routes for all authenticated users with feature restriction
    Route::middleware([\App\Http\Middleware\CheckSubscription::class . ':Time Schedule'])->group(function () {
        Route::post('/time-schedule', [App\Http\Controllers\TimeScheduleController::class, 'store'])->name('time-schedule.store');
        Route::put('/time-schedule/{id}', [App\Http\Controllers\TimeScheduleController::class, 'update'])->name('time-schedule.update');
        Route::delete('/time-schedule/{id}', [App\Http\Controllers\TimeScheduleController::class, 'destroy'])->name('time-schedule.destroy');
        Route::post('/time-schedule/calculate-weight', [App\Http\Controllers\TimeScheduleController::class, 'calculateWeight'])->name('time-schedule.calculate-weight');
        Route::post('/time-schedule/remaining-weight', [App\Http\Controllers\TimeScheduleController::class, 'getRemainingWeight'])->name('time-schedule.remaining-weight');
    });
});

// Visitor pages
Route::get('/welcome', function () {
    return view('visitor.welcome');
})->name('visitor.welcome');

Route::get('/tutorial', function () {
    return view('visitor.tutorial');
})->name('visitor.tutorial');

Route::get('/packages', function () {
    return view('visitor.packages');
})->name('visitor.packages');

Route::get('/terms', function () {
    return view('visitor.terms');
})->name('visitor.terms');

// Koleksi routes
Route::get('/koleksi', [App\Http\Controllers\KoleksiController::class, 'index'])->name('visitor.koleksi');
Route::get('/koleksi/upah', [App\Http\Controllers\KoleksiController::class, 'upah'])->name('visitor.koleksi.upah');
Route::get('/koleksi/upah/{id}', [App\Http\Controllers\KoleksiController::class, 'upahDetail'])->name('visitor.koleksi.upah.detail');
Route::get('/koleksi/bahan', [App\Http\Controllers\KoleksiController::class, 'bahan'])->name('visitor.koleksi.bahan');
Route::get('/koleksi/bahan/{id}', [App\Http\Controllers\KoleksiController::class, 'bahanDetail'])->name('visitor.koleksi.bahan.detail');
Route::get('/koleksi/alat', [App\Http\Controllers\KoleksiController::class, 'alat'])->name('visitor.koleksi.alat');
Route::get('/koleksi/alat/{id}', [App\Http\Controllers\KoleksiController::class, 'alatDetail'])->name('visitor.koleksi.alat.detail');
Route::get('/koleksi/ahsp', [App\Http\Controllers\KoleksiController::class, 'ahsp'])->name('visitor.koleksi.ahsp');
Route::get('/koleksi/ahsp/{id}', [App\Http\Controllers\KoleksiController::class, 'ahspDetail'])->name('visitor.koleksi.ahsp.detail');

// Payment Gateway Callback
Route::post('/payments/callback', [\App\Http\Controllers\PaymentController::class, 'callback'])
    ->name('payments.callback')
    ->withoutMiddleware([
        \App\Http\Middleware\VerifyCsrfToken::class,
        \App\Http\Middleware\Authenticate::class,
        \Illuminate\Session\Middleware\StartSession::class,
        \Illuminate\Session\Middleware\AuthenticateSession::class,
        \Illuminate\View\Middleware\ShareErrorsFromSession::class
    ]);

// Webhook route tanpa middleware
Route::post('/xendit-webhook', function (Illuminate\Http\Request $request) {
    \Illuminate\Support\Facades\Log::info('Xendit webhook received directly', [
        'headers' => $request->headers->all(),
        'content' => $request->getContent(),
        'ip' => $request->ip()
    ]);

    // Get the Payment controller
    $controller = new \App\Http\Controllers\PaymentController();

    // Process webhook
    return $controller->xenditWebhook($request);
})->withoutMiddleware(\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class);

// Fallback route for 404 errors
Route::fallback(function () {
    return view('404');
});
