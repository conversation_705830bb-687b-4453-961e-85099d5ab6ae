/**
 * Sistem Notifikasi untuk RAB Estimator
 * Menggantikan alert dengan toast notification yang lebih menarik
 */

// Global toast container
const toastContainer = document.getElementById("toast-container");

// Tipe notifikasi
const TOAST_TYPES = {
    SUCCESS: "success",
    ERROR: "error",
    WARNING: "warning",
    INFO: "info",
};

// Icon untuk setiap tipe notifikasi
const TOAST_ICONS = {
    [TOAST_TYPES.SUCCESS]: "fa-check-circle",
    [TOAST_TYPES.ERROR]: "fa-times-circle",
    [TOAST_TYPES.WARNING]: "fa-exclamation-triangle",
    [TOAST_TYPES.INFO]: "fa-info-circle",
};

// Judul default untuk setiap tipe notifikasi
const TOAST_TITLES = {
    [TOAST_TYPES.SUCCESS]: "Berhasil",
    [TOAST_TYPES.ERROR]: "<PERSON><PERSON><PERSON>",
    [TOAST_TYPES.WARNING]: "Peringatan",
    [TOAST_TYPES.INFO]: "Informasi",
};

/**
 * Buat dan tampilkan toast notification
 * @param {string} message - Pesan yang akan ditampilkan
 * @param {string} type - Tipe toast (success, error, warning, info)
 * @param {string|null} title - Judul toast (optional)
 * @param {number} duration - Durasi toast dalam milidetik (default: 5000)
 * @param {Function|null} callback - Fungsi yang akan dipanggil saat toast ditutup
 * @param {string|null} upgradeUrl - URL untuk tombol upgrade (optional)
 */
window.showToast = function (
    message,
    type = TOAST_TYPES.INFO,
    title = null,
    duration = 5000,
    callback = null,
    upgradeUrl = null
) {
    // Jika container belum ada, buat container
    if (!toastContainer) {
        const container = document.createElement("div");
        container.id = "toast-container";
        container.className = "toast-container";
        document.body.appendChild(container);
    }

    // Buat elemen toast
    const toast = document.createElement("div");
    toast.className = `toast toast-${type}`;

    // Set CSS variable untuk durasi progress bar
    toast.style.setProperty("--duration", `${duration}ms`);

    // Icon element
    const icon = document.createElement("i");
    icon.className = `fas ${TOAST_ICONS[type]} toast-icon`;

    // Content element
    const content = document.createElement("div");
    content.className = "toast-content";

    // Title element
    const titleEl = document.createElement("div");
    titleEl.className = "toast-title";
    titleEl.textContent = title || TOAST_TITLES[type];

    // Message element
    const messageEl = document.createElement("div");
    messageEl.className = "toast-message";
    messageEl.textContent = message;

    // Tombol Upgrade jika upgradeUrl diberikan dan tipe info
    let upgradeBtn = null;
    if (type === TOAST_TYPES.INFO && upgradeUrl) {
        upgradeBtn = document.createElement("a");
        upgradeBtn.href = upgradeUrl;
        upgradeBtn.target = "_self";
        upgradeBtn.className =
            "toast-upgrade-btn bg-blue-500 hover:bg-blue-600 text-white font-semibold px-4 py-2 rounded mt-3 inline-block transition-colors duration-150";
        upgradeBtn.textContent = "Upgrade Paket";
        // Tambahkan margin atas jika ada pesan
        messageEl.style.marginBottom = "0.5rem";
    }

    // Close button (tetap ada tapi disembunyikan)
    const closeBtn = document.createElement("button");
    closeBtn.className = "toast-close hidden";
    closeBtn.innerHTML = "&times;";
    closeBtn.setAttribute("aria-label", "Close");

    // Timer element - menampilkan sisa waktu
    const timerEl = document.createElement("div");
    timerEl.className = "toast-timer";
    const secondsRemaining = Math.floor(duration / 1000);
    timerEl.textContent = `${secondsRemaining}s`;

    // Progress bar
    const progressBar = document.createElement("div");
    progressBar.className = "toast-progress";

    // Membangun struktur toast
    content.appendChild(titleEl);
    content.appendChild(messageEl);
    if (upgradeBtn) content.appendChild(upgradeBtn);
    toast.appendChild(icon);
    toast.appendChild(content);
    toast.appendChild(timerEl);
    toast.appendChild(closeBtn);
    toast.appendChild(progressBar);

    // Tambahkan toast ke container
    document.getElementById("toast-container").appendChild(toast);

    // Fungsi untuk menghapus toast
    const removeToast = () => {
        toast.style.animation = "toastOut 0.3s ease forwards";
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
                if (callback) callback();
            }
        }, 300);
    };

    // Tambahkan event listener untuk tombol close
    closeBtn.addEventListener("click", () => {
        removeToast();
    });

    // Update timer countdown
    let countdown = secondsRemaining;
    const timerInterval = setInterval(() => {
        countdown--;
        if (countdown > 0) {
            timerEl.textContent = `${countdown}s`;
        } else {
            timerEl.textContent = "";
            clearInterval(timerInterval);
        }
    }, 1000);

    // Set timeout untuk menghapus toast setelah durasi tertentu
    const timeoutId = setTimeout(() => {
        clearInterval(timerInterval);
        removeToast();
    }, duration);

    // Jika di-hover, pause timeout dan timer
    toast.addEventListener("mouseenter", () => {
        clearTimeout(timeoutId);
        clearInterval(timerInterval);
        progressBar.style.animationPlayState = "paused";
    });

    // Jika mouse leave, resume timeout dan timer
    toast.addEventListener("mouseleave", () => {
        progressBar.style.animationPlayState = "running";
        // Hitung sisa waktu berdasarkan width progress bar
        const remainingPercent =
            parseInt(getComputedStyle(progressBar).width) /
            parseInt(getComputedStyle(toast).width);
        const remainingTime = Math.max(1000, duration * remainingPercent);

        // Restart timer dengan sisa waktu
        let remainingSecs = Math.ceil(remainingTime / 1000);
        timerEl.textContent = `${remainingSecs}s`;

        const newTimerInterval = setInterval(() => {
            remainingSecs--;
            if (remainingSecs > 0) {
                timerEl.textContent = `${remainingSecs}s`;
            } else {
                timerEl.textContent = "";
                clearInterval(newTimerInterval);
            }
        }, 1000);

        setTimeout(() => {
            clearInterval(newTimerInterval);
            removeToast();
        }, remainingTime);
    });

    return toast;
};

/**
 * Function shortcuts untuk berbagai tipe notifikasi
 */
window.showSuccessToast = function (
    message,
    title = null,
    callback = null,
    duration = 3000
) {
    return window.showToast(
        message,
        TOAST_TYPES.SUCCESS,
        title,
        duration,
        callback
    );
};

window.showErrorToast = function (
    message,
    title = null,
    callback = null,
    duration = 5000
) {
    return window.showToast(
        message,
        TOAST_TYPES.ERROR,
        title,
        duration,
        callback
    );
};

window.showWarningToast = function (
    message,
    title = null,
    callback = null,
    duration = 4000
) {
    return window.showToast(
        message,
        TOAST_TYPES.WARNING,
        title,
        duration,
        callback
    );
};

window.showInfoToast = function (
    message,
    title = null,
    callback = null,
    duration = 3000,
    upgradeUrl = null
) {
    return window.showToast(
        message,
        TOAST_TYPES.INFO,
        title,
        duration,
        callback,
        upgradeUrl
    );
};

/**
 * Override fungsi alert standar dengan toast notification
 * Ini akan menggantikan semua panggilan alert() dengan showInfoToast()
 */
window.originalAlert = window.alert;
window.alert = function (message) {
    window.showInfoToast(message, null, null, 3000);
};

/**
 * Utility untuk menangani respons AJAX
 * @param {Object} response - Response dari fetch API
 * @param {Function|null} successCallback - Callback jika sukses
 * @param {boolean} reload - Reload halaman setelah sukses
 * @param {number} duration - Durasi toast dalam milidetik (default: 3000)
 */
window.handleApiResponse = function (
    response,
    successCallback = null,
    reload = false,
    duration = 3000
) {
    if (response.success) {
        window.showSuccessToast(
            response.message,
            null,
            () => {
                if (reload) window.location.reload();
                else if (successCallback) successCallback();
            },
            duration
        );
    } else {
        window.showErrorToast(
            response.message || "Terjadi kesalahan pada server",
            null,
            null,
            5000
        );
    }
};

// Gantikan showCustomAlert dengan showToast
if (typeof window.showCustomAlert === "function") {
    window.originalShowCustomAlert = window.showCustomAlert;
    window.showCustomAlert = function (message, callback = null) {
        window.showInfoToast(message, null, callback, 3000);
    };
}

// Inisialisasi saat DOM sudah dimuat
document.addEventListener("DOMContentLoaded", function () {
    // Siapkan override untuk XMLHttpRequest jika diperlukan
    const originalXhrOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function (method, url) {
        this.addEventListener("load", function () {
            if (
                this.getResponseHeader("Content-Type") &&
                this.getResponseHeader("Content-Type").includes(
                    "application/json"
                )
            ) {
                try {
                    const response = JSON.parse(this.responseText);
                    if (response.message) {
                        if (response.success) {
                            window.showSuccessToast(
                                response.message,
                                null,
                                null,
                                3000
                            );
                        } else {
                            window.showErrorToast(
                                response.message,
                                null,
                                null,
                                5000
                            );
                        }
                    }
                } catch (e) {
                    // Bukan JSON atau tidak ada property message
                }
            }
        });
        originalXhrOpen.apply(this, arguments);
    };
});
