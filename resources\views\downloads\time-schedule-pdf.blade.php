<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Time Schedule - {{ $project->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        .project-info {
            margin-bottom: 20px;
        }
        .project-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .project-info td {
            padding: 5px;
            border: none;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th,
        .table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .table td.left {
            text-align: left;
        }
        .gantt-bar {
            background-color: #4CAF50;
            height: 20px;
            border-radius: 3px;
            position: relative;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TIME SCHEDULE PELAKSANAAN PEKERJAAN</h1>
    </div>

    <div class="project-info">
        <table>
            <tr>
                <td style="width: 15%"><strong>Nama Proyek:</strong></td>
                <td style="width: 35%">{{ $project->name }}</td>
                <td style="width: 15%"><strong>Lokasi:</strong></td>
                <td style="width: 35%">{{ $project->location }}</td>
            </tr>
        </table>
    </div>

    @if(in_array('time-schedule-table', $exportOptions))
    <div class="section">
        <h3>Tabel Time Schedule</h3>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 5%">No</th>
                    <th style="width: 30%">Item Pekerjaan</th>
                    <th style="width: 15%">Tanggal Mulai</th>
                    <th style="width: 15%">Tanggal Selesai</th>
                    <th style="width: 10%">Durasi (Hari)</th>
                    <th style="width: 10%">Bobot (%)</th>
                    <th style="width: 15%">Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($timeSchedules as $index => $schedule)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td class="left">{{ $schedule->item_pekerjaan }}</td>
                    <td>{{ \Carbon\Carbon::parse($schedule->tanggal_mulai)->format('d/m/Y') }}</td>
                    <td>{{ \Carbon\Carbon::parse($schedule->tanggal_selesai)->format('d/m/Y') }}</td>
                    <td>{{ $schedule->durasi }}</td>
                    <td>{{ number_format($schedule->bobot, 2) }}%</td>
                    <td>{{ $schedule->status ?? 'Belum Dimulai' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(in_array('time-schedule-gantt', $exportOptions))
    <div class="section page-break">
        <h3>Gantt Chart</h3>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 5%">No</th>
                    <th style="width: 25%">Item Pekerjaan</th>
                    <th style="width: 70%">Timeline</th>
                </tr>
            </thead>
            <tbody>
                @foreach($timeSchedules as $index => $schedule)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td class="left">{{ $schedule->item_pekerjaan }}</td>
                    <td>
                        <div style="position: relative; height: 25px; background-color: #f9f9f9;">
                            <div class="gantt-bar" style="width: {{ $schedule->bobot }}%; margin-top: 2px;">
                                <span style="color: white; font-size: 10px; position: absolute; left: 5px; top: 3px;">
                                    {{ number_format($schedule->bobot, 1) }}%
                                </span>
                            </div>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    @if(in_array('kurva-s', $exportOptions))
    <div class="section page-break">
        <h3>Kurva S</h3>
        <div style="text-align: center; padding: 50px;">
            <p><em>Kurva S akan ditampilkan berdasarkan data time schedule yang tersedia.</em></p>
            <p>Total Bobot Kumulatif: {{ $timeSchedules->sum('bobot') }}%</p>
        </div>
    </div>
    @endif
</body>
</html>
