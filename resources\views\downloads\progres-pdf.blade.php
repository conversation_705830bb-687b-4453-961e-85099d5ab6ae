<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Progres - {{ $project->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }
        .project-info {
            margin-bottom: 20px;
        }
        .project-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .project-info td {
            padding: 5px;
            border: none;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th,
        .table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .table td.left {
            text-align: left;
        }
        .table td.right {
            text-align: right;
        }
        .progress-bar {
            background-color: #2196F3;
            height: 20px;
            border-radius: 3px;
            position: relative;
        }
        .page-break {
            page-break-before: always;
        }
        .summary-box {
            border: 2px solid #333;
            padding: 15px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>LAPORAN PROGRES PELAKSANAAN PEKERJAAN</h1>
    </div>

    <div class="project-info">
        <table>
            <tr>
                <td style="width: 15%"><strong>Nama Proyek:</strong></td>
                <td style="width: 35%">{{ $project->name }}</td>
                <td style="width: 15%"><strong>Lokasi:</strong></td>
                <td style="width: 35%">{{ $project->location }}</td>
            </tr>
        </table>
    </div>

    @if(in_array('progres-mingguan', $exportOptions))
    <div class="section">
        <h3>Progres Mingguan</h3>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 5%">No</th>
                    <th style="width: 25%">Item Pekerjaan</th>
                    <th style="width: 15%">Periode</th>
                    <th style="width: 15%">Volume Realisasi</th>
                    <th style="width: 15%">Jumlah Harga</th>
                    <th style="width: 10%">Bobot (%)</th>
                    <th style="width: 15%">Keterangan</th>
                </tr>
            </thead>
            <tbody>
                @php $totalBobot = 0; $totalHarga = 0; @endphp
                @foreach($progresData->where('jenis_periode', 'mingguan') as $index => $progres)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td class="left">{{ $progres->itemPekerjaan->uraian_item ?? 'Item tidak ditemukan' }}</td>
                    <td>{{ $progres->periode }}</td>
                    <td class="right">{{ number_format($progres->volume_realisasi, 2) }}</td>
                    <td class="right">Rp {{ number_format($progres->jumlah_harga, 0, ',', '.') }}</td>
                    <td>{{ number_format($progres->bobot, 2) }}%</td>
                    <td class="left">{{ $progres->keterangan ?? '-' }}</td>
                </tr>
                @php 
                    $totalBobot += $progres->bobot; 
                    $totalHarga += $progres->jumlah_harga;
                @endphp
                @endforeach
                <tr style="background-color: #f0f0f0; font-weight: bold;">
                    <td colspan="4">TOTAL MINGGUAN</td>
                    <td class="right">Rp {{ number_format($totalHarga, 0, ',', '.') }}</td>
                    <td>{{ number_format($totalBobot, 2) }}%</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>
    </div>
    @endif

    @if(in_array('progres-bulanan', $exportOptions))
    <div class="section page-break">
        <h3>Progres Bulanan</h3>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 5%">No</th>
                    <th style="width: 25%">Item Pekerjaan</th>
                    <th style="width: 15%">Periode</th>
                    <th style="width: 15%">Volume Realisasi</th>
                    <th style="width: 15%">Jumlah Harga</th>
                    <th style="width: 10%">Bobot (%)</th>
                    <th style="width: 15%">Keterangan</th>
                </tr>
            </thead>
            <tbody>
                @php $totalBobotBulanan = 0; $totalHargaBulanan = 0; @endphp
                @foreach($progresData->where('jenis_periode', 'bulanan') as $index => $progres)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td class="left">{{ $progres->itemPekerjaan->uraian_item ?? 'Item tidak ditemukan' }}</td>
                    <td>{{ $progres->periode }}</td>
                    <td class="right">{{ number_format($progres->volume_realisasi, 2) }}</td>
                    <td class="right">Rp {{ number_format($progres->jumlah_harga, 0, ',', '.') }}</td>
                    <td>{{ number_format($progres->bobot, 2) }}%</td>
                    <td class="left">{{ $progres->keterangan ?? '-' }}</td>
                </tr>
                @php 
                    $totalBobotBulanan += $progres->bobot; 
                    $totalHargaBulanan += $progres->jumlah_harga;
                @endphp
                @endforeach
                <tr style="background-color: #f0f0f0; font-weight: bold;">
                    <td colspan="4">TOTAL BULANAN</td>
                    <td class="right">Rp {{ number_format($totalHargaBulanan, 0, ',', '.') }}</td>
                    <td>{{ number_format($totalBobotBulanan, 2) }}%</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>
    </div>
    @endif

    @if(in_array('progres-kumulatif', $exportOptions))
    <div class="section page-break">
        <h3>Progres Kumulatif</h3>
        
        <div class="summary-box">
            <h4>Ringkasan Progres Kumulatif</h4>
            <table style="width: 100%; border: none;">
                <tr>
                    <td style="border: none; width: 50%;">
                        <strong>Total Progres Fisik:</strong> {{ number_format($progresData->sum('bobot'), 2) }}%
                    </td>
                    <td style="border: none; width: 50%;">
                        <strong>Total Nilai:</strong> Rp {{ number_format($progresData->sum('jumlah_harga'), 0, ',', '.') }}
                    </td>
                </tr>
            </table>
        </div>

        <table class="table">
            <thead>
                <tr>
                    <th style="width: 5%">No</th>
                    <th style="width: 30%">Item Pekerjaan</th>
                    <th style="width: 15%">Total Volume</th>
                    <th style="width: 20%">Total Nilai</th>
                    <th style="width: 15%">Progres (%)</th>
                    <th style="width: 15%">Visual Progress</th>
                </tr>
            </thead>
            <tbody>
                @php 
                    $groupedProgres = $progresData->groupBy('item_pekerjaan_id');
                    $itemNumber = 1;
                @endphp
                @foreach($groupedProgres as $itemId => $progresGroup)
                @php
                    $totalVolume = $progresGroup->sum('volume_realisasi');
                    $totalNilai = $progresGroup->sum('jumlah_harga');
                    $totalBobot = $progresGroup->sum('bobot');
                    $firstProgres = $progresGroup->first();
                @endphp
                <tr>
                    <td>{{ $itemNumber++ }}</td>
                    <td class="left">{{ $firstProgres->itemPekerjaan->uraian_item ?? 'Item tidak ditemukan' }}</td>
                    <td class="right">{{ number_format($totalVolume, 2) }}</td>
                    <td class="right">Rp {{ number_format($totalNilai, 0, ',', '.') }}</td>
                    <td>{{ number_format($totalBobot, 2) }}%</td>
                    <td>
                        <div style="position: relative; height: 20px; background-color: #e0e0e0; border-radius: 3px;">
                            <div class="progress-bar" style="width: {{ min($totalBobot, 100) }}%;">
                                <span style="color: white; font-size: 10px; position: absolute; left: 5px; top: 2px;">
                                    {{ number_format($totalBobot, 1) }}%
                                </span>
                            </div>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
</body>
</html>
